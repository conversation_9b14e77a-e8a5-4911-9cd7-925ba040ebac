using Bogus;
using FluentAssertions;
using TriangleNet.Geometry;
using TriangleNet.Meshing;
using Workflow.Section.Activities;
using Xunit;

namespace Workflow.Tests.Section.Activities.CreateInitialSliFileActivityTests;

[Trait("CreateInitialSliFileActivity", "PerformTriangulation")]
public class PerformTriangulationTests
{
    private readonly Faker _faker = new();

    [Fact(DisplayName = "When polygon is valid, should return mesh with triangles")]
    public void WhenPolygonIsValid_ShouldReturnMeshWithTriangles()
    {
        // Arrange
        var polygon = CreateValidSquarePolygon();

        // Act
        var result = InvokePerformTriangulation(polygon);

        // Assert
        result.Should().NotBeNull();
        result.Triangles.Should().NotBeEmpty();
        result.Triangles.Count().Should().BeGreaterThan(0);
    }

    [Fact(DisplayName = "When polygon has duplicate vertices, should handle gracefully")]
    public void WhenPolygonHasDuplicateVertices_ShouldHandleGracefully()
    {
        // Arrange
        var polygon = CreatePolygonWithDuplicateVertices();

        // Act
        var action = () => InvokePerformTriangulation(polygon);

        // Assert - Should either succeed or throw meaningful exception
        var result = action.Should().NotThrow().Subject;
        if (result != null)
        {
            result.Triangles.Should().NotBeNull();
        }
    }

    [Fact(DisplayName = "When polygon has degenerate segments, should handle gracefully")]
    public void WhenPolygonHasDegenerateSegments_ShouldHandleGracefully()
    {
        // Arrange
        var polygon = CreatePolygonWithDegenerateSegments();

        // Act
        var action = () => InvokePerformTriangulation(polygon);

        // Assert - Should either succeed or throw meaningful exception
        var result = action.Should().NotThrow().Subject;
        if (result != null)
        {
            result.Triangles.Should().NotBeNull();
        }
    }

    [Fact(DisplayName = "When polygon is empty, should throw InvalidOperationException")]
    public void WhenPolygonIsEmpty_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var polygon = new Polygon();

        // Act
        var action = () => InvokePerformTriangulation(polygon);

        // Assert
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("*triangulation*");
    }

    [Fact(DisplayName = "When polygon has insufficient points, should throw InvalidOperationException")]
    public void WhenPolygonHasInsufficientPoints_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var polygon = new Polygon();
        polygon.Points.Add(new Vertex(0, 0));
        polygon.Points.Add(new Vertex(1, 0));

        // Act
        var action = () => InvokePerformTriangulation(polygon);

        // Assert
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("*triangulation*");
    }

    [Fact(DisplayName = "When polygon has complex geometry, should triangulate successfully")]
    public void WhenPolygonHasComplexGeometry_ShouldTriangulateSuccessfully()
    {
        // Arrange
        var polygon = CreateComplexPolygon();

        // Act
        var result = InvokePerformTriangulation(polygon);

        // Assert
        result.Should().NotBeNull();
        result.Triangles.Should().NotBeEmpty();
        result.Triangles.Count().Should().BeGreaterThan(2);
    }

    [Fact(DisplayName = "When polygon has overlapping segments, should handle gracefully")]
    public void WhenPolygonHasOverlappingSegments_ShouldHandleGracefully()
    {
        // Arrange
        var polygon = CreatePolygonWithOverlappingSegments();

        // Act
        var action = () => InvokePerformTriangulation(polygon);

        // Assert - Should either succeed or throw meaningful exception
        var result = action.Should().NotThrow().Subject;
        if (result != null)
        {
            result.Triangles.Should().NotBeNull();
        }
    }

    [Fact(DisplayName = "When polygon has very small coordinates, should handle precision correctly")]
    public void WhenPolygonHasVerySmallCoordinates_ShouldHandlePrecisionCorrectly()
    {
        // Arrange
        var polygon = CreatePolygonWithSmallCoordinates();

        // Act
        var result = InvokePerformTriangulation(polygon);

        // Assert
        result.Should().NotBeNull();
        result.Triangles.Should().NotBeEmpty();
    }

    [Fact(DisplayName = "When polygon has very large coordinates, should handle correctly")]
    public void WhenPolygonHasVeryLargeCoordinates_ShouldHandleCorrectly()
    {
        // Arrange
        var polygon = CreatePolygonWithLargeCoordinates();

        // Act
        var result = InvokePerformTriangulation(polygon);

        // Assert
        result.Should().NotBeNull();
        result.Triangles.Should().NotBeEmpty();
    }

    private IMesh InvokePerformTriangulation(Polygon polygon)
    {
        var method = typeof(Workflow.Section.Activities.CreateInitialSliFileActivity)
            .GetMethod("PerformTriangulation", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
        
        return (IMesh)method.Invoke(null, new object[] { polygon });
    }

    private Polygon CreateValidSquarePolygon()
    {
        var polygon = new Polygon();
        
        // Create a simple square contour
        var vertices = new[]
        {
            new Vertex(0, 0, 1),
            new Vertex(10, 0, 1),
            new Vertex(10, 10, 1),
            new Vertex(0, 10, 1)
        };
        
        polygon.Add(new Contour(vertices, 1));
        
        // Add some internal segments
        polygon.Add(new Segment(new Vertex(2, 2), new Vertex(8, 2), 1), 1);
        polygon.Add(new Segment(new Vertex(8, 2), new Vertex(8, 8), 1), 1);
        
        return polygon;
    }

    private Polygon CreatePolygonWithDuplicateVertices()
    {
        var polygon = new Polygon();
        
        // Add duplicate points
        polygon.Points.Add(new Vertex(0, 0));
        polygon.Points.Add(new Vertex(0, 0)); // Duplicate
        polygon.Points.Add(new Vertex(10, 0));
        polygon.Points.Add(new Vertex(10, 10));
        polygon.Points.Add(new Vertex(0, 10));
        
        return polygon;
    }

    private Polygon CreatePolygonWithDegenerateSegments()
    {
        var polygon = new Polygon();
        
        var vertices = new[]
        {
            new Vertex(0, 0, 1),
            new Vertex(10, 0, 1),
            new Vertex(10, 10, 1),
            new Vertex(0, 10, 1)
        };
        
        polygon.Add(new Contour(vertices, 1));
        
        // Add degenerate segment (same start and end point)
        polygon.Add(new Segment(new Vertex(5, 5), new Vertex(5, 5), 1), 1);
        
        return polygon;
    }

    private Polygon CreateComplexPolygon()
    {
        var polygon = new Polygon();
        
        // Create an L-shaped contour
        var vertices = new[]
        {
            new Vertex(0, 0, 1),
            new Vertex(20, 0, 1),
            new Vertex(20, 10, 1),
            new Vertex(10, 10, 1),
            new Vertex(10, 20, 1),
            new Vertex(0, 20, 1)
        };
        
        polygon.Add(new Contour(vertices, 1));
        
        // Add internal segments
        polygon.Add(new Segment(new Vertex(5, 5), new Vertex(15, 5), 1), 1);
        polygon.Add(new Segment(new Vertex(5, 15), new Vertex(5, 5), 1), 1);
        
        return polygon;
    }

    private Polygon CreatePolygonWithOverlappingSegments()
    {
        var polygon = new Polygon();
        
        var vertices = new[]
        {
            new Vertex(0, 0, 1),
            new Vertex(10, 0, 1),
            new Vertex(10, 10, 1),
            new Vertex(0, 10, 1)
        };
        
        polygon.Add(new Contour(vertices, 1));
        
        // Add overlapping segments
        polygon.Add(new Segment(new Vertex(2, 2), new Vertex(8, 2), 1), 1);
        polygon.Add(new Segment(new Vertex(2, 2), new Vertex(8, 2), 1), 1); // Duplicate
        
        return polygon;
    }

    private Polygon CreatePolygonWithSmallCoordinates()
    {
        var polygon = new Polygon();
        
        var vertices = new[]
        {
            new Vertex(0.0001, 0.0001, 1),
            new Vertex(0.001, 0.0001, 1),
            new Vertex(0.001, 0.001, 1),
            new Vertex(0.0001, 0.001, 1)
        };
        
        polygon.Add(new Contour(vertices, 1));
        
        return polygon;
    }

    private Polygon CreatePolygonWithLargeCoordinates()
    {
        var polygon = new Polygon();
        
        var vertices = new[]
        {
            new Vertex(1000000, 1000000, 1),
            new Vertex(1000010, 1000000, 1),
            new Vertex(1000010, 1000010, 1),
            new Vertex(1000000, 1000010, 1)
        };
        
        polygon.Add(new Contour(vertices, 1));
        
        return polygon;
    }
}

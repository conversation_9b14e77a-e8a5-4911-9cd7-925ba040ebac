using System.Text;
using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.Section;
using Bogus;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using FluentAssertions;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using Moq;
using Moq.AutoMock;
using Workflow.Section.Activities;
using Xunit;
using static Workflow.Constants.SectionWorkFlow;
using File = Application.Apis._Shared.File;

namespace Workflow.Tests.Section.Activities.CreateInitialSliFileActivity;

[Trait("CreateInitialSliFileActivity", "OnExecuteAsync")]
public class OnExecuteAsyncTests
{
    private readonly AutoMocker _mocker = new();
    private readonly Faker _faker = new();
    private readonly Workflow.Section.Activities.CreateInitialSliFileActivity _activity;
    private readonly ActivityExecutionContext _context;

    public OnExecuteAsyncTests()
    {
        _context = new ActivityExecutionContextBuilder().Context;
        _activity = _mocker.CreateInstance<CreateInitialSliFileActivity>();
    }

    [Fact(DisplayName = "When section is not found, should return Done")]
    public async Task WhenSectionIsNotFound_ShouldReturnDone()
    {
        // Arrange
        var command = new Domain.Messages.Commands.Section.CreateSliFile { SectionId = Guid.NewGuid() };
        _context.SetTransientVariable(Variables.Command, command);

        _mocker.GetMock<IClientsApiService>()
            .Setup(x => x.GetSectionById(command.SectionId))
            .ReturnsAsync((GetSectionByIdResponse)null);

        // Act
        var result = await _activity.ExecuteAsync(_context);

        // Assert
        result.Should().BeOfType<DoneResult>();
    }

    [Fact(DisplayName = "When section has no reviews, should return Done")]
    public async Task WhenSectionHasNoReviews_ShouldReturnDone()
    {
        // Arrange
        var command = new Domain.Messages.Commands.Section.CreateSliFile { SectionId = Guid.NewGuid() };
        _context.SetTransientVariable(Variables.Command, command);

        var section = new GetSectionByIdResponse
        {
            Id = command.SectionId,
            Reviews = new List<SectionReview>()
        };

        _mocker.GetMock<IClientsApiService>()
            .Setup(x => x.GetSectionById(command.SectionId))
            .ReturnsAsync(section);

        // Act
        var result = await _activity.ExecuteAsync(_context);

        // Assert
        result.Should().BeOfType<DoneResult>();
    }

    [Fact(DisplayName = "When section review has valid DXF with triangulation issue, should handle gracefully")]
    public async Task WhenSectionReviewHasValidDxfWithTriangulationIssue_ShouldHandleGracefully()
    {
        // Arrange
        var command = new Domain.Messages.Commands.Section.CreateSliFile { SectionId = Guid.NewGuid() };
        _context.SetTransientVariable(Variables.Command, command);

        var dxfWithTriangulationIssue = CreateDxfWithTriangulationIssue();
        var section = new GetSectionByIdResponse
        {
            Id = command.SectionId,
            Reviews = new List<SectionReview>
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    Drawing = new File { Base64 = ConvertDxfToBase64(dxfWithTriangulationIssue) },
                    ConstructionStages = new List<ConstructionStage>()
                }
            }
        };

        _mocker.GetMock<IClientsApiService>()
            .Setup(x => x.GetSectionById(command.SectionId))
            .ReturnsAsync(section);

        _mocker.GetMock<IClientsApiService>()
            .Setup(x => x.AddSliToSectionReview(It.IsAny<AddSliFileRequest>()))
            .ReturnsAsync(new HttpResponseMessage(System.Net.HttpStatusCode.OK));

        // Act
        var result = await _activity.ExecuteAsync(_context);

        // Assert
        result.Should().BeOfType<DoneResult>();
    }

    [Fact(DisplayName = "When section review has valid DXF, should create SLI successfully")]
    public async Task WhenSectionReviewHasValidDxf_ShouldCreateSliSuccessfully()
    {
        // Arrange
        var command = new Domain.Messages.Commands.Section.CreateSliFile { SectionId = Guid.NewGuid() };
        _context.SetTransientVariable(Variables.Command, command);

        var validDxf = CreateValidDxf();
        var section = new GetSectionByIdResponse
        {
            Id = command.SectionId,
            Reviews = new List<SectionReview>
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    Drawing = new File { Base64 = ConvertDxfToBase64(validDxf) },
                    ConstructionStages = new List<ConstructionStage>()
                }
            }
        };

        _mocker.GetMock<IClientsApiService>()
            .Setup(x => x.GetSectionById(command.SectionId))
            .ReturnsAsync(section);

        _mocker.GetMock<IClientsApiService>()
            .Setup(x => x.AddSliToSectionReview(It.IsAny<AddSliFileRequest>()))
            .ReturnsAsync(new HttpResponseMessage(System.Net.HttpStatusCode.OK));

        // Act
        var result = await _activity.ExecuteAsync(_context);

        // Assert
        result.Should().BeOfType<DoneResult>();
        _mocker.GetMock<IClientsApiService>()
            .Verify(x => x.AddSliToSectionReview(It.IsAny<AddSliFileRequest>()), Times.Once);
    }

    [Fact(DisplayName = "When construction stage has valid DXF, should create SLI successfully")]
    public async Task WhenConstructionStageHasValidDxf_ShouldCreateSliSuccessfully()
    {
        // Arrange
        var command = new Domain.Messages.Commands.Section.CreateSliFile { SectionId = Guid.NewGuid() };
        _context.SetTransientVariable(Variables.Command, command);

        var validDxf = CreateValidDxf();
        var section = new GetSectionByIdResponse
        {
            Id = command.SectionId,
            Reviews = new List<SectionReview>
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    Drawing = null,
                    ConstructionStages = new List<ConstructionStage>
                    {
                        new()
                        {
                            Id = Guid.NewGuid(),
                            Drawing = new File { Base64 = ConvertDxfToBase64(validDxf) }
                        }
                    }
                }
            }
        };

        _mocker.GetMock<IClientsApiService>()
            .Setup(x => x.GetSectionById(command.SectionId))
            .ReturnsAsync(section);

        _mocker.GetMock<IClientsApiService>()
            .Setup(x => x.AddSliToSectionReview(It.IsAny<AddSliFileRequest>()))
            .ReturnsAsync(new HttpResponseMessage(System.Net.HttpStatusCode.OK));

        // Act
        var result = await _activity.ExecuteAsync(_context);

        // Assert
        result.Should().BeOfType<DoneResult>();
        _mocker.GetMock<IClientsApiService>()
            .Verify(x => x.AddSliToSectionReview(It.IsAny<AddSliFileRequest>()), Times.Once);
    }

    [Fact(DisplayName = "When API call fails, should return Fault")]
    public async Task WhenApiCallFails_ShouldReturnFault()
    {
        // Arrange
        var command = new Domain.Messages.Commands.Section.CreateSliFile { SectionId = Guid.NewGuid() };
        _context.SetTransientVariable(Variables.Command, command);

        var validDxf = CreateValidDxf();
        var section = new GetSectionByIdResponse
        {
            Id = command.SectionId,
            Reviews = new List<SectionReview>
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    Drawing = new File { Base64 = ConvertDxfToBase64(validDxf) },
                    ConstructionStages = new List<ConstructionStage>()
                }
            }
        };

        _mocker.GetMock<IClientsApiService>()
            .Setup(x => x.GetSectionById(command.SectionId))
            .ReturnsAsync(section);

        _mocker.GetMock<IClientsApiService>()
            .Setup(x => x.AddSliToSectionReview(It.IsAny<AddSliFileRequest>()))
            .ReturnsAsync(new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest));

        // Act
        var result = await _activity.ExecuteAsync(_context);

        // Assert
        result.Should().BeOfType<FaultResult>();
    }

    [Fact(DisplayName = "When exception occurs, should return Fault")]
    public async Task WhenExceptionOccurs_ShouldReturnFault()
    {
        // Arrange
        var command = new Domain.Messages.Commands.Section.CreateSliFile { SectionId = Guid.NewGuid() };
        _context.SetTransientVariable(Variables.Command, command);

        _mocker.GetMock<IClientsApiService>()
            .Setup(x => x.GetSectionById(command.SectionId))
            .ThrowsAsync(new Exception("Test exception"));

        // Act
        var result = await _activity.ExecuteAsync(_context);

        // Assert
        result.Should().BeOfType<FaultResult>();
    }

    private DxfFile CreateValidDxf()
    {
        var dxf = new DxfFile();
        dxf.Header.Version = DxfAcadVersion.R2018;

        // Add external boundary
        var externalVertices = new[]
        {
            new DxfLwPolylineVertex { X = 0, Y = 0 },
            new DxfLwPolylineVertex { X = 100, Y = 0 },
            new DxfLwPolylineVertex { X = 100, Y = 100 },
            new DxfLwPolylineVertex { X = 0, Y = 100 }
        };
        var externalPolyline = new DxfLwPolyline(externalVertices) { Layer = "external" };
        dxf.Entities.Add(externalPolyline);

        // Add material segments
        var materialVertices = new[]
        {
            new DxfLwPolylineVertex { X = 10, Y = 10 },
            new DxfLwPolylineVertex { X = 90, Y = 10 },
            new DxfLwPolylineVertex { X = 90, Y = 90 }
        };
        var materialPolyline = new DxfLwPolyline(materialVertices) { Layer = "material" };
        dxf.Entities.Add(materialPolyline);

        return dxf;
    }

    private DxfFile CreateDxfWithTriangulationIssue()
    {
        var dxf = new DxfFile();
        dxf.Header.Version = DxfAcadVersion.R2018;

        // Add external boundary
        var externalVertices = new[]
        {
            new DxfLwPolylineVertex { X = 0, Y = 0 },
            new DxfLwPolylineVertex { X = 100, Y = 0 },
            new DxfLwPolylineVertex { X = 100, Y = 100 },
            new DxfLwPolylineVertex { X = 0, Y = 100 }
        };
        var externalPolyline = new DxfLwPolyline(externalVertices) { Layer = "external" };
        dxf.Entities.Add(externalPolyline);

        // Add problematic material segments that could cause triangulation issues
        // Duplicate vertices
        var materialVertices = new[]
        {
            new DxfLwPolylineVertex { X = 10, Y = 10 },
            new DxfLwPolylineVertex { X = 10, Y = 10 }, // Duplicate
            new DxfLwPolylineVertex { X = 90, Y = 10 },
            new DxfLwPolylineVertex { X = 90, Y = 10 }  // Duplicate
        };
        var materialPolyline = new DxfLwPolyline(materialVertices) { Layer = "material" };
        dxf.Entities.Add(materialPolyline);

        return dxf;
    }

    private string ConvertDxfToBase64(DxfFile dxf)
    {
        using var stream = new MemoryStream();
        dxf.Save(stream);
        return Convert.ToBase64String(stream.ToArray());
    }
}

using Bogus;
using FluentAssertions;
using Slide.Core.Objects.Sli;
using TriangleNet.Geometry;
using Workflow.Section.Activities;
using Xunit;

namespace Workflow.Tests.Section.Activities.CreateInitialSliFileActivityTests;

[Trait("CreateInitialSliFileActivity", "BuildRobustPolygon")]
public class BuildRobustPolygonTests
{
    private readonly Faker _faker = new();

    [Fact(DisplayName = "When external vertices are valid, should create contour")]
    public void WhenExternalVerticesAreValid_ShouldCreateContour()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 10, Y = 0 },
            new() { X = 10, Y = 10 },
            new() { X = 0, Y = 10 }
        };
        var materials = new List<(Vertice, int)>();

        // Act
        var result = InvokeBuildRobustPolygon(externalVertices, materials);

        // Assert
        result.Should().NotBeNull();
        // Note: TriangleNet.Geometry.Polygon doesn't expose Contours collection directly
        // We can verify the polygon was created by checking if it has points
        result.Points.Should().HaveCountGreaterOrEqualTo(4);
    }

    [Fact(DisplayName = "When external vertices have duplicates, should deduplicate")]
    public void WhenExternalVerticesHaveDuplicates_ShouldDeduplicate()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 0, Y = 0 }, // Duplicate
            new() { X = 10, Y = 0 },
            new() { X = 10, Y = 10 },
            new() { X = 0, Y = 10 }
        };
        var materials = new List<(Vertice, int)>();

        // Act
        var result = InvokeBuildRobustPolygon(externalVertices, materials);

        // Assert
        result.Should().NotBeNull();
        // Verify duplicates were removed by checking unique points
        result.Points.Should().HaveCount(4); // Duplicates removed
    }

    [Fact(DisplayName = "When external vertices are insufficient, should not create contour")]
    public void WhenExternalVerticesAreInsufficient_ShouldNotCreateContour()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 10, Y = 0 }
        };
        var materials = new List<(Vertice, int)>();

        // Act
        var result = InvokeBuildRobustPolygon(externalVertices, materials);

        // Assert
        result.Should().NotBeNull();
        // With insufficient vertices, no contour should be created
        result.Points.Should().HaveCountLessThan(3);
    }

    [Fact(DisplayName = "When materials have valid segments, should add segments")]
    public void WhenMaterialsHaveValidSegments_ShouldAddSegments()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 10, Y = 0 },
            new() { X = 10, Y = 10 },
            new() { X = 0, Y = 10 }
        };
        var materials = new List<(Vertice, int)>
        {
            (new Vertice { X = 2, Y = 2 }, 1),
            (new Vertice { X = 8, Y = 2 }, 1),
            (new Vertice { X = 8, Y = 8 }, 1)
        };

        // Act
        var result = InvokeBuildRobustPolygon(externalVertices, materials);

        // Assert
        result.Should().NotBeNull();
        result.Segments.Should().HaveCountGreaterThan(0);
        result.Points.Should().HaveCountGreaterThan(4); // External + material points
    }

    [Fact(DisplayName = "When materials have duplicate segments, should deduplicate")]
    public void WhenMaterialsHaveDuplicateSegments_ShouldDeduplicate()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 10, Y = 0 },
            new() { X = 10, Y = 10 },
            new() { X = 0, Y = 10 }
        };
        var materials = new List<(Vertice, int)>
        {
            (new Vertice { X = 2, Y = 2 }, 1),
            (new Vertice { X = 8, Y = 2 }, 1),
            (new Vertice { X = 2, Y = 2 }, 2), // Same point, different group
            (new Vertice { X = 8, Y = 2 }, 2)  // Same point, different group
        };

        // Act
        var result = InvokeBuildRobustPolygon(externalVertices, materials);

        // Assert
        result.Should().NotBeNull();
        result.Segments.Should().HaveCount(1); // Only one unique segment
    }

    [Fact(DisplayName = "When materials have degenerate segments, should skip them")]
    public void WhenMaterialsHaveDegenerateSegments_ShouldSkipThem()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 10, Y = 0 },
            new() { X = 10, Y = 10 },
            new() { X = 0, Y = 10 }
        };
        var materials = new List<(Vertice, int)>
        {
            (new Vertice { X = 2, Y = 2 }, 1),
            (new Vertice { X = 2, Y = 2 }, 1), // Degenerate segment (same point)
            (new Vertice { X = 8, Y = 2 }, 2),
            (new Vertice { X = 8, Y = 8 }, 2)
        };

        // Act
        var result = InvokeBuildRobustPolygon(externalVertices, materials);

        // Assert
        result.Should().NotBeNull();
        result.Segments.Should().HaveCount(1); // Only the valid segment
    }

    [Fact(DisplayName = "When materials have null vertices, should handle gracefully")]
    public void WhenMaterialsHaveNullVertices_ShouldHandleGracefully()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 10, Y = 0 },
            new() { X = 10, Y = 10 },
            new() { X = 0, Y = 10 }
        };
        var materials = new List<(Vertice, int)>
        {
            (new Vertice { X = 2, Y = 2 }, 1),
            (null, 1), // Null vertex
            (new Vertice { X = 8, Y = 2 }, 1)
        };

        // Act
        var result = InvokeBuildRobustPolygon(externalVertices, materials);

        // Assert
        result.Should().NotBeNull();
        // Should handle null vertices gracefully without throwing
    }

    [Fact(DisplayName = "When materials have very close coordinates, should handle precision")]
    public void WhenMaterialsHaveVeryCloseCoordinates_ShouldHandlePrecision()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 10, Y = 0 },
            new() { X = 10, Y = 10 },
            new() { X = 0, Y = 10 }
        };
        var materials = new List<(Vertice, int)>
        {
            (new Vertice { X = 2.0000000001, Y = 2.0000000001 }, 1),
            (new Vertice { X = 2.0000000002, Y = 2.0000000002 }, 1), // Very close
            (new Vertice { X = 8, Y = 2 }, 1)
        };

        // Act
        var result = InvokeBuildRobustPolygon(externalVertices, materials);

        // Assert
        result.Should().NotBeNull();
        // Should handle precision correctly
    }

    [Fact(DisplayName = "When multiple material groups exist, should process all")]
    public void WhenMultipleMaterialGroupsExist_ShouldProcessAll()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 20, Y = 0 },
            new() { X = 20, Y = 20 },
            new() { X = 0, Y = 20 }
        };
        var materials = new List<(Vertice, int)>
        {
            // Group 1
            (new Vertice { X = 2, Y = 2 }, 1),
            (new Vertice { X = 8, Y = 2 }, 1),
            (new Vertice { X = 8, Y = 8 }, 1),
            // Group 2
            (new Vertice { X = 12, Y = 12 }, 2),
            (new Vertice { X = 18, Y = 12 }, 2),
            (new Vertice { X = 18, Y = 18 }, 2)
        };

        // Act
        var result = InvokeBuildRobustPolygon(externalVertices, materials);

        // Assert
        result.Should().NotBeNull();
        result.Segments.Should().HaveCountGreaterThan(2); // Should have segments from both groups
    }

    [Fact(DisplayName = "When no materials provided, should create polygon with only external contour")]
    public void WhenNoMaterialsProvided_ShouldCreatePolygonWithOnlyExternalContour()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 10, Y = 0 },
            new() { X = 10, Y = 10 },
            new() { X = 0, Y = 10 }
        };
        var materials = new List<(Vertice, int)>();

        // Act
        var result = InvokeBuildRobustPolygon(externalVertices, materials);

        // Assert
        result.Should().NotBeNull();
        // Verify polygon was created with external vertices only
        result.Segments.Should().BeEmpty();
        result.Points.Should().HaveCount(4); // Only external vertices
    }

    private Polygon InvokeBuildRobustPolygon(List<Vertice> externalVertices, List<(Vertice, int)> materials)
    {
        var method = typeof(Workflow.Section.Activities.CreateInitialSliFileActivity)
            .GetMethod("BuildRobustPolygon", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
        
        return (Polygon)method.Invoke(null, new object[] { externalVertices, materials });
    }
}

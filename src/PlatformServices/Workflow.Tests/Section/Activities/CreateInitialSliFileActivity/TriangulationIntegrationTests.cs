using FluentAssertions;
using Slide.Core.Objects.Sli;
using TriangleNet.Geometry;
using Workflow.Section.Activities;
using Xunit;

namespace Workflow.Tests.Section.Activities.CreateInitialSliFileActivityTests;

[Trait("CreateInitialSliFileActivity", "TriangulationIntegration")]
public class TriangulationIntegrationTests
{
    [Fact(DisplayName = "When polygon has problematic geometry that caused original bug, should handle gracefully")]
    public void WhenPolygonHasProblematicGeometryThatCausedOriginalBug_ShouldHandleGracefully()
    {
        // Arrange - Create a scenario that would have caused the original triangulation bug
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 100, Y = 0 },
            new() { X = 100, Y = 100 },
            new() { X = 0, Y = 100 }
        };

        // Materials with duplicate vertices that would cause "Topological inconsistency after splitting a segment"
        var materials = new List<(Vertice, int)>
        {
            (new Vertice { X = 10, Y = 10 }, 1),
            (new Vertice { X = 10, Y = 10 }, 1), // Duplicate - would cause original bug
            (new Vertice { X = 50, Y = 10 }, 1),
            (new Vertice { X = 50, Y = 10 }, 1), // Duplicate - would cause original bug
            (new Vertice { X = 90, Y = 10 }, 1),
            (new Vertice { X = 90, Y = 50 }, 1),
            (new Vertice { X = 90, Y = 50 }, 1), // Duplicate - would cause original bug
        };

        // Act - This should not throw the original "Topological inconsistency after splitting a segment" exception
        var action = () => InvokeBuildRobustPolygon(externalVertices, materials);

        // Assert
        var polygon = action.Should().NotThrow().Subject;
        polygon.Should().NotBeNull();
        
        // Verify the polygon was cleaned and is valid for triangulation
        var triangulationAction = () => InvokePerformTriangulation(polygon);
        var mesh = triangulationAction.Should().NotThrow().Subject;
        mesh.Should().NotBeNull();
        mesh.Triangles.Should().NotBeEmpty();
    }

    [Fact(DisplayName = "When polygon has degenerate segments, should clean and triangulate successfully")]
    public void WhenPolygonHasDegenerateSegments_ShouldCleanAndTriangulateSuccessfully()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 20, Y = 0 },
            new() { X = 20, Y = 20 },
            new() { X = 0, Y = 20 }
        };

        // Materials with degenerate segments (same start and end point)
        var materials = new List<(Vertice, int)>
        {
            (new Vertice { X = 5, Y = 5 }, 1),
            (new Vertice { X = 5, Y = 5 }, 1), // Degenerate segment
            (new Vertice { X = 15, Y = 5 }, 2),
            (new Vertice { X = 15, Y = 15 }, 2),
        };

        // Act
        var polygon = InvokeBuildRobustPolygon(externalVertices, materials);
        var mesh = InvokePerformTriangulation(polygon);

        // Assert
        polygon.Should().NotBeNull();
        mesh.Should().NotBeNull();
        mesh.Triangles.Should().NotBeEmpty();
    }

    [Fact(DisplayName = "When polygon has very close vertices within tolerance, should merge and triangulate")]
    public void WhenPolygonHasVeryCloseVerticesWithinTolerance_ShouldMergeAndTriangulate()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 30, Y = 0 },
            new() { X = 30, Y = 30 },
            new() { X = 0, Y = 30 }
        };

        // Materials with vertices very close to each other (within tolerance)
        var materials = new List<(Vertice, int)>
        {
            (new Vertice { X = 10.0000000001, Y = 10.0000000001 }, 1),
            (new Vertice { X = 10.0000000002, Y = 10.0000000002 }, 1), // Very close
            (new Vertice { X = 20, Y = 10 }, 1),
            (new Vertice { X = 20, Y = 20 }, 1),
        };

        // Act
        var polygon = InvokeBuildRobustPolygon(externalVertices, materials);
        var mesh = InvokePerformTriangulation(polygon);

        // Assert
        polygon.Should().NotBeNull();
        mesh.Should().NotBeNull();
        mesh.Triangles.Should().NotBeEmpty();
    }

    [Fact(DisplayName = "When polygon has complex overlapping segments, should handle gracefully")]
    public void WhenPolygonHasComplexOverlappingSegments_ShouldHandleGracefully()
    {
        // Arrange
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 40, Y = 0 },
            new() { X = 40, Y = 40 },
            new() { X = 0, Y = 40 }
        };

        // Materials with overlapping segments
        var materials = new List<(Vertice, int)>
        {
            // First group
            (new Vertice { X = 5, Y = 5 }, 1),
            (new Vertice { X = 15, Y = 5 }, 1),
            (new Vertice { X = 15, Y = 15 }, 1),
            
            // Second group with overlapping segment
            (new Vertice { X = 5, Y = 5 }, 2),
            (new Vertice { X = 15, Y = 5 }, 2), // Same segment as above
            (new Vertice { X = 25, Y = 15 }, 2),
        };

        // Act
        var polygon = InvokeBuildRobustPolygon(externalVertices, materials);
        var mesh = InvokePerformTriangulation(polygon);

        // Assert
        polygon.Should().NotBeNull();
        mesh.Should().NotBeNull();
        mesh.Triangles.Should().NotBeEmpty();
    }

    [Fact(DisplayName = "When polygon validation fails initially but cleaning succeeds, should triangulate")]
    public void WhenPolygonValidationFailsInitiallyButCleaningSucceeds_ShouldTriangulate()
    {
        // Arrange - Create a polygon that will fail initial validation but can be cleaned
        var externalVertices = new List<Vertice>
        {
            new() { X = 0, Y = 0 },
            new() { X = 50, Y = 0 },
            new() { X = 50, Y = 50 },
            new() { X = 0, Y = 50 }
        };

        // Materials with multiple issues: duplicates, degenerate segments, very close points
        var materials = new List<(Vertice, int)>
        {
            (new Vertice { X = 10, Y = 10 }, 1),
            (new Vertice { X = 10, Y = 10 }, 1), // Duplicate
            (new Vertice { X = 10.0000000001, Y = 10.0000000001 }, 1), // Very close
            (new Vertice { X = 20, Y = 10 }, 1),
            (new Vertice { X = 20, Y = 10 }, 1), // Duplicate
            (new Vertice { X = 30, Y = 10 }, 2),
            (new Vertice { X = 30, Y = 10 }, 2), // Degenerate segment
            (new Vertice { X = 40, Y = 20 }, 2),
        };

        // Act - Should clean the polygon and triangulate successfully
        var polygon = InvokeBuildRobustPolygon(externalVertices, materials);
        var mesh = InvokePerformTriangulation(polygon);

        // Assert
        polygon.Should().NotBeNull();
        mesh.Should().NotBeNull();
        mesh.Triangles.Should().NotBeEmpty();
    }

    private Polygon InvokeBuildRobustPolygon(List<Vertice> externalVertices, List<(Vertice, int)> materials)
    {
        var method = typeof(Workflow.Section.Activities.CreateInitialSliFileActivity)
            .GetMethod("BuildRobustPolygon", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
        
        return (Polygon)method.Invoke(null, new object[] { externalVertices, materials });
    }

    private TriangleNet.Meshing.IMesh InvokePerformTriangulation(Polygon polygon)
    {
        var method = typeof(Workflow.Section.Activities.CreateInitialSliFileActivity)
            .GetMethod("PerformTriangulation", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
        
        return (TriangleNet.Meshing.IMesh)method.Invoke(null, new object[] { polygon });
    }
}

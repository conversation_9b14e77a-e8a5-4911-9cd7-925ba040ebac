using System.Text;
using Application.Apis.Clients;
using Bogus;
using Elsa.Services.Models;
using FluentAssertions;
using Geometry.Core;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using Moq;
using Moq.AutoMock;
using Slide.Core.Objects.Sli;
using TriangleNet.Geometry;
using Workflow.Section.Activities;
using Xunit;
using File = Application.Apis._Shared.File;

namespace Workflow.Tests.Section.Activities.CreateInitialSliFileActivity;

[Trait("CreateInitialSliFileActivity", "MountSliFile")]
public class MountSliFileTests
{
    private readonly AutoMocker _mocker = new();
    private readonly Faker _faker = new();
    private readonly Workflow.Section.Activities.CreateInitialSliFileActivity _activity;

    public MountSliFileTests()
    {
        _activity = _mocker.CreateInstance<CreateInitialSliFileActivity>();
    }

    [Fact(DisplayName = "When DXF file is null, should return null")]
    public void WhenDxfFileIsNull_ShouldReturnNull()
    {
        // Arrange
        File dxfFile = null;

        // Act
        var result = InvokeMountSliFile(dxfFile);

        // Assert
        result.Should().BeNull();
    }

    [Fact(DisplayName = "When DXF file has empty Base64, should return null")]
    public void WhenDxfFileHasEmptyBase64_ShouldReturnNull()
    {
        // Arrange
        var dxfFile = new File { Base64 = string.Empty };

        // Act
        var result = InvokeMountSliFile(dxfFile);

        // Assert
        result.Should().BeNull();
    }

    [Fact(DisplayName = "When DXF has no external vertices, should return null")]
    public void WhenDxfHasNoExternalVertices_ShouldReturnNull()
    {
        // Arrange
        var dxf = CreateBasicDxfFile();
        // Add only material entities, no external
        AddMaterialPolyline(dxf, new[] { (0.0, 0.0), (10.0, 0.0), (10.0, 10.0) });
        
        var dxfFile = new File { Base64 = ConvertDxfToBase64(dxf) };

        // Act
        var result = InvokeMountSliFile(dxfFile);

        // Assert
        result.Should().BeNull();
    }

    [Fact(DisplayName = "When DXF has no material vertices, should return null")]
    public void WhenDxfHasNoMaterialVertices_ShouldReturnNull()
    {
        // Arrange
        var dxf = CreateBasicDxfFile();
        // Add only external entities, no materials
        AddExternalPolyline(dxf, new[] { (0.0, 0.0), (100.0, 0.0), (100.0, 100.0), (0.0, 100.0) });
        
        var dxfFile = new File { Base64 = ConvertDxfToBase64(dxf) };

        // Act
        var result = InvokeMountSliFile(dxfFile);

        // Assert
        result.Should().BeNull();
    }

    [Fact(DisplayName = "When DXF has valid external and material vertices, should create SLI file")]
    public void WhenDxfHasValidExternalAndMaterialVertices_ShouldCreateSliFile()
    {
        // Arrange
        var dxf = CreateBasicDxfFile();
        AddExternalPolyline(dxf, new[] { (0.0, 0.0), (100.0, 0.0), (100.0, 100.0), (0.0, 100.0) });
        AddMaterialPolyline(dxf, new[] { (10.0, 10.0), (90.0, 10.0), (90.0, 90.0) });
        
        var dxfFile = new File { Base64 = ConvertDxfToBase64(dxf) };

        // Act
        var result = InvokeMountSliFile(dxfFile);

        // Assert
        result.Should().NotBeNull();
        result.Base64.Should().NotBeNullOrEmpty();
        result.Name.Should().EndWith(".sli");
    }

    [Fact(DisplayName = "When DXF has duplicate vertices, should handle gracefully")]
    public void WhenDxfHasDuplicateVertices_ShouldHandleGracefully()
    {
        // Arrange
        var dxf = CreateBasicDxfFile();
        AddExternalPolyline(dxf, new[] { (0.0, 0.0), (100.0, 0.0), (100.0, 100.0), (0.0, 100.0) });
        // Add material with duplicate vertices
        AddMaterialPolyline(dxf, new[] { (10.0, 10.0), (10.0, 10.0), (90.0, 10.0), (90.0, 10.0) });
        
        var dxfFile = new File { Base64 = ConvertDxfToBase64(dxf) };

        // Act
        var result = InvokeMountSliFile(dxfFile);

        // Assert
        result.Should().NotBeNull();
        result.Base64.Should().NotBeNullOrEmpty();
    }

    [Fact(DisplayName = "When DXF has line entities, should process correctly")]
    public void WhenDxfHasLineEntities_ShouldProcessCorrectly()
    {
        // Arrange
        var dxf = CreateBasicDxfFile();
        AddExternalPolyline(dxf, new[] { (0.0, 0.0), (100.0, 0.0), (100.0, 100.0), (0.0, 100.0) });
        AddMaterialLine(dxf, (10.0, 10.0), (90.0, 10.0));
        
        var dxfFile = new File { Base64 = ConvertDxfToBase64(dxf) };

        // Act
        var result = InvokeMountSliFile(dxfFile);

        // Assert
        result.Should().NotBeNull();
        result.Base64.Should().NotBeNullOrEmpty();
    }

    [Fact(DisplayName = "When DXF has mixed entity types, should process all correctly")]
    public void WhenDxfHasMixedEntityTypes_ShouldProcessAllCorrectly()
    {
        // Arrange
        var dxf = CreateBasicDxfFile();
        AddExternalPolyline(dxf, new[] { (0.0, 0.0), (100.0, 0.0), (100.0, 100.0), (0.0, 100.0) });
        AddExternalLine(dxf, (50.0, 0.0), (50.0, 100.0));
        AddMaterialPolyline(dxf, new[] { (10.0, 10.0), (40.0, 10.0), (40.0, 40.0) });
        AddMaterialLine(dxf, (60.0, 10.0), (90.0, 10.0));
        
        var dxfFile = new File { Base64 = ConvertDxfToBase64(dxf) };

        // Act
        var result = InvokeMountSliFile(dxfFile);

        // Assert
        result.Should().NotBeNull();
        result.Base64.Should().NotBeNullOrEmpty();
    }

    private File InvokeMountSliFile(File dxfFile)
    {
        var method = typeof(Workflow.Section.Activities.CreateInitialSliFileActivity)
            .GetMethod("MountSliFile", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        return (File)method.Invoke(_activity, new object[] { dxfFile });
    }

    private DxfFile CreateBasicDxfFile()
    {
        var dxf = new DxfFile();
        dxf.Header.Version = DxfAcadVersion.R2018;
        return dxf;
    }

    private void AddExternalPolyline(DxfFile dxf, (double x, double y)[] points)
    {
        var vertices = points.Select(p => new DxfLwPolylineVertex(new DxfPoint(p.x, p.y, 0))).ToList();
        var polyline = new DxfLwPolyline(vertices) { Layer = "external" };
        dxf.Entities.Add(polyline);
    }

    private void AddMaterialPolyline(DxfFile dxf, (double x, double y)[] points)
    {
        var vertices = points.Select(p => new DxfLwPolylineVertex(new DxfPoint(p.x, p.y, 0))).ToList();
        var polyline = new DxfLwPolyline(vertices) { Layer = "material" };
        dxf.Entities.Add(polyline);
    }

    private void AddExternalLine(DxfFile dxf, (double x, double y) start, (double x, double y) end)
    {
        var line = new DxfLine(new DxfPoint(start.x, start.y, 0), new DxfPoint(end.x, end.y, 0)) { Layer = "external" };
        dxf.Entities.Add(line);
    }

    private void AddMaterialLine(DxfFile dxf, (double x, double y) start, (double x, double y) end)
    {
        var line = new DxfLine(new DxfPoint(start.x, start.y, 0), new DxfPoint(end.x, end.y, 0)) { Layer = "material" };
        dxf.Entities.Add(line);
    }

    private string ConvertDxfToBase64(DxfFile dxf)
    {
        using var stream = new MemoryStream();
        dxf.Save(stream);
        return Convert.ToBase64String(stream.ToArray());
    }
}

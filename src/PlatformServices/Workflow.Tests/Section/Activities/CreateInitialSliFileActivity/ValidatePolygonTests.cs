using Bogus;
using FluentAssertions;
using TriangleNet.Geometry;
using Workflow.Section.Activities;
using Xunit;

namespace Workflow.Tests.Section.Activities.CreateInitialSliFileActivity;

[Trait("CreateInitialSliFileActivity", "ValidatePolygon")]
public class ValidatePolygonTests
{
    private readonly Faker _faker = new();

    [Fact(DisplayName = "When polygon has sufficient points and valid segments, should return true")]
    public void WhenPolygonHasSufficientPointsAndValidSegments_ShouldReturnTrue()
    {
        // Arrange
        var polygon = CreateValidPolygon();

        // Act
        var result = InvokeValidatePolygon(polygon);

        // Assert
        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When polygon has fewer than 3 points, should return false")]
    public void WhenPolygonHasFewerThan3Points_ShouldReturnFalse()
    {
        // Arrange
        var polygon = new Polygon();
        polygon.Points.Add(new Vertex(0, 0));
        polygon.Points.Add(new Vertex(1, 0));

        // Act
        var result = InvokeValidatePolygon(polygon);

        // Assert
        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When polygon has duplicate points, should return false")]
    public void WhenPolygonHasDuplicatePoints_ShouldReturnFalse()
    {
        // Arrange
        var polygon = new Polygon();
        polygon.Points.Add(new Vertex(0, 0));
        polygon.Points.Add(new Vertex(0, 0)); // Duplicate
        polygon.Points.Add(new Vertex(1, 0));
        polygon.Points.Add(new Vertex(1, 1));

        // Act
        var result = InvokeValidatePolygon(polygon);

        // Assert
        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When polygon has degenerate segments, should return false")]
    public void WhenPolygonHasDegenerateSegments_ShouldReturnFalse()
    {
        // Arrange
        var polygon = new Polygon();
        polygon.Points.Add(new Vertex(0, 0));
        polygon.Points.Add(new Vertex(1, 0));
        polygon.Points.Add(new Vertex(1, 1));
        polygon.Points.Add(new Vertex(0, 1));
        
        // Add degenerate segment (same start and end point)
        polygon.Add(new Segment(new Vertex(0.5, 0.5), new Vertex(0.5, 0.5), 1), 1);

        // Act
        var result = InvokeValidatePolygon(polygon);

        // Assert
        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When polygon has very close duplicate points within tolerance, should return false")]
    public void WhenPolygonHasVeryCloseDuplicatePointsWithinTolerance_ShouldReturnFalse()
    {
        // Arrange
        var polygon = new Polygon();
        polygon.Points.Add(new Vertex(0, 0));
        polygon.Points.Add(new Vertex(1e-12, 1e-12)); // Within tolerance
        polygon.Points.Add(new Vertex(1, 0));
        polygon.Points.Add(new Vertex(1, 1));

        // Act
        var result = InvokeValidatePolygon(polygon);

        // Assert
        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When polygon has very close segments within tolerance, should return false")]
    public void WhenPolygonHasVeryCloseSegmentsWithinTolerance_ShouldReturnFalse()
    {
        // Arrange
        var polygon = new Polygon();
        polygon.Points.Add(new Vertex(0, 0));
        polygon.Points.Add(new Vertex(1, 0));
        polygon.Points.Add(new Vertex(1, 1));
        polygon.Points.Add(new Vertex(0, 1));
        
        // Add segment with very close start and end points
        polygon.Add(new Segment(new Vertex(0.5, 0.5), new Vertex(0.5 + 1e-12, 0.5 + 1e-12), 1), 1);

        // Act
        var result = InvokeValidatePolygon(polygon);

        // Assert
        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When polygon validation throws exception, should return false")]
    public void WhenPolygonValidationThrowsException_ShouldReturnFalse()
    {
        // Arrange
        var polygon = new Polygon();
        // Create a polygon that might cause validation issues
        polygon.Points.Add(new Vertex(double.NaN, 0));
        polygon.Points.Add(new Vertex(1, 0));
        polygon.Points.Add(new Vertex(1, 1));

        // Act
        var result = InvokeValidatePolygon(polygon);

        // Assert
        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When polygon has large coordinates, should validate correctly")]
    public void WhenPolygonHasLargeCoordinates_ShouldValidateCorrectly()
    {
        // Arrange
        var polygon = new Polygon();
        polygon.Points.Add(new Vertex(1000000, 1000000));
        polygon.Points.Add(new Vertex(1000001, 1000000));
        polygon.Points.Add(new Vertex(1000001, 1000001));
        polygon.Points.Add(new Vertex(1000000, 1000001));

        // Act
        var result = InvokeValidatePolygon(polygon);

        // Assert
        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When polygon has small coordinates, should validate correctly")]
    public void WhenPolygonHasSmallCoordinates_ShouldValidateCorrectly()
    {
        // Arrange
        var polygon = new Polygon();
        polygon.Points.Add(new Vertex(0.001, 0.001));
        polygon.Points.Add(new Vertex(0.002, 0.001));
        polygon.Points.Add(new Vertex(0.002, 0.002));
        polygon.Points.Add(new Vertex(0.001, 0.002));

        // Act
        var result = InvokeValidatePolygon(polygon);

        // Assert
        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When polygon has mixed valid and invalid elements, should return false")]
    public void WhenPolygonHasMixedValidAndInvalidElements_ShouldReturnFalse()
    {
        // Arrange
        var polygon = new Polygon();
        // Add valid points
        polygon.Points.Add(new Vertex(0, 0));
        polygon.Points.Add(new Vertex(1, 0));
        polygon.Points.Add(new Vertex(1, 1));
        polygon.Points.Add(new Vertex(0, 1));
        
        // Add duplicate point
        polygon.Points.Add(new Vertex(0, 0));

        // Act
        var result = InvokeValidatePolygon(polygon);

        // Assert
        result.Should().BeFalse();
    }

    private bool InvokeValidatePolygon(Polygon polygon)
    {
        var method = typeof(Workflow.Section.Activities.CreateInitialSliFileActivity)
            .GetMethod("ValidatePolygon", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
        
        return (bool)method.Invoke(null, new object[] { polygon });
    }

    private Polygon CreateValidPolygon()
    {
        var polygon = new Polygon();
        
        // Add valid, non-duplicate points
        polygon.Points.Add(new Vertex(0, 0));
        polygon.Points.Add(new Vertex(10, 0));
        polygon.Points.Add(new Vertex(10, 10));
        polygon.Points.Add(new Vertex(0, 10));
        
        // Add valid segments
        polygon.Add(new Segment(new Vertex(2, 2), new Vertex(8, 2), 1), 1);
        polygon.Add(new Segment(new Vertex(8, 2), new Vertex(8, 8), 1), 1);
        
        return polygon;
    }
}

[Trait("CreateInitialSliFileActivity", "CleanPolygon")]
public class CleanPolygonTests
{
    private readonly Faker _faker = new();

    [Fact(DisplayName = "When polygon has duplicate points, should remove duplicates")]
    public void WhenPolygonHasDuplicatePoints_ShouldRemoveDuplicates()
    {
        // Arrange
        var polygon = new Polygon();
        polygon.Points.Add(new Vertex(0, 0));
        polygon.Points.Add(new Vertex(0, 0)); // Duplicate
        polygon.Points.Add(new Vertex(1, 0));
        polygon.Points.Add(new Vertex(1, 1));
        polygon.Points.Add(new Vertex(1, 1)); // Duplicate

        // Act
        var result = InvokeCleanPolygon(polygon);

        // Assert
        result.Should().NotBeNull();
        result.Points.Should().HaveCount(3); // Duplicates removed
    }

    [Fact(DisplayName = "When polygon has degenerate segments, should remove them")]
    public void WhenPolygonHasDegenerateSegments_ShouldRemoveThem()
    {
        // Arrange
        var polygon = new Polygon();
        polygon.Points.Add(new Vertex(0, 0));
        polygon.Points.Add(new Vertex(1, 0));
        polygon.Points.Add(new Vertex(1, 1));
        
        // Add valid segment
        polygon.Add(new Segment(new Vertex(0, 0), new Vertex(1, 0), 1), 1);
        // Add degenerate segment
        polygon.Add(new Segment(new Vertex(0.5, 0.5), new Vertex(0.5, 0.5), 1), 1);

        // Act
        var result = InvokeCleanPolygon(polygon);

        // Assert
        result.Should().NotBeNull();
        result.Segments.Should().HaveCount(1); // Only valid segment remains
    }

    [Fact(DisplayName = "When polygon has duplicate segments, should remove duplicates")]
    public void WhenPolygonHasDuplicateSegments_ShouldRemoveDuplicates()
    {
        // Arrange
        var polygon = new Polygon();
        polygon.Points.Add(new Vertex(0, 0));
        polygon.Points.Add(new Vertex(1, 0));
        polygon.Points.Add(new Vertex(1, 1));
        
        // Add duplicate segments
        polygon.Add(new Segment(new Vertex(0, 0), new Vertex(1, 0), 1), 1);
        polygon.Add(new Segment(new Vertex(0, 0), new Vertex(1, 0), 1), 1); // Duplicate
        polygon.Add(new Segment(new Vertex(1, 0), new Vertex(0, 0), 1), 1); // Reverse duplicate

        // Act
        var result = InvokeCleanPolygon(polygon);

        // Assert
        result.Should().NotBeNull();
        result.Segments.Should().HaveCount(1); // Duplicates removed
    }

    [Fact(DisplayName = "When polygon has valid contours, should preserve them")]
    public void WhenPolygonHasValidContours_ShouldPreserveThem()
    {
        // Arrange
        var polygon = new Polygon();
        var vertices = new[]
        {
            new Vertex(0, 0, 1),
            new Vertex(10, 0, 1),
            new Vertex(10, 10, 1),
            new Vertex(0, 10, 1)
        };
        polygon.Add(new Contour(vertices, 1));

        // Act
        var result = InvokeCleanPolygon(polygon);

        // Assert
        result.Should().NotBeNull();
        // Verify contour was preserved by checking points
        result.Points.Should().HaveCountGreaterOrEqualTo(4);
    }

    [Fact(DisplayName = "When polygon cleaning throws exception, should return original")]
    public void WhenPolygonCleaningThrowsException_ShouldReturnOriginal()
    {
        // Arrange
        var polygon = new Polygon();
        polygon.Points.Add(new Vertex(0, 0));

        // Act
        var result = InvokeCleanPolygon(polygon);

        // Assert
        result.Should().NotBeNull();
        // Should return a polygon (either cleaned or original)
    }

    private Polygon InvokeCleanPolygon(Polygon polygon)
    {
        var method = typeof(Workflow.Section.Activities.CreateInitialSliFileActivity)
            .GetMethod("CleanPolygon", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
        
        return (Polygon)method.Invoke(null, new object[] { polygon });
    }
}

using Geometry.Core;
using Slide.Core.Objects.Sli;
using System.Text;

namespace Slide.Core
{
    public class SliFile
    {
        public ModelDescription ModelDescription { get; private set; } = new();
        public SideForceFunction SideForceFunction { get; private set; } = new();
        public List<MaterialType> MaterialTypes { get; private set; } = new();
        public ShansepFunctionSection ShansepVariableFunctions { get; private set; } = new();
        public ModifiedSnowdenStrengthFunctions ModifiedSnowdenStrengthFunctions { get; private set; } = new();
        public DesignStandardArrayDefinition DesignStandardArrayDefinition { get; private set; } = new();
        public MaxConverageSearch MaxCoverageSearch { get; set; } = new();
        public GridSearch GridSearch { get; set; } = new();
        public ThreepointSearch ThreepointSearch { get; set; } = new();
        public AutoslopeSearch AutoslopeSearch { get; set; } = new();
        public CuckooSearch CuckooSearch { get; set; } = new();
        public SimulatedAnnealingSearch SimulatedAnnealingSearch { get; set; } = new();
        public PsoSearch PsoSearch { get; set; } = new();
        public BlockSearch BlockSearch { get; set; } = new();
        public PathSearch PathSearch { get; set; } = new();
        public ThreeDSectionalInfo ThreeDSectionalInfo { get; private set; } = new();
        public int UserDefGeoManufacturerFileArray { get; private set; }
        public ProbabilitySettings ProbabilitySettings { get; private set; } = new();
        public List<StrengthFunctionDefinition> StrengthFunctions { get; private set; } = new();
        public GeneralizedAnisotropicStrengthFunctions GeneralizedAnisotropicStrengthFunctions { get; private set; } = new();
        public StartDiscreteStrengthFns StartDiscreteStrengthFns { get; private set; } = new();
        public EmptyClass EndDiscreteStrengthFns { get; private set; }
        public ShansepMaterialDependentFunctions ShansepMaterialDependentFunctions { get; private set; } = new();
        public EmptyClass UserDefinedSupport { get; private set; }
        public EmptyClass UserDefinedWaterMethod { get; private set; }
        public EmptyClass UserDefinedBc { get; private set; }
        public List<AnchorType> AnchorTypes { get; private set; } = new()
        {
            new()
            {
                Name = "anchor1"
            },
            new()
            {
                Name = "anchor2"
            },
            new()
            {
                Name = "anchor3"
            },
            new()
            {
                Name = "anchor4"
            },
            new()
            {
                Name = "anchor5"
            }
        };

        public EmptyClass FrictionFactor { get; private set; }
        public AnchorRspileAssignments AnchorRspileAssignments { get; private set; } = new()
        {
            NumMatAssign = new()
            {
                new(),
                new(),
                new(),
                new(),
                new()
            }
        };
        public AnchorRspilePileData AnchorRspilePileData { get; private set; } = new()
        {
            AnchorRspileData = new()
            {
                new(),
                new(),
                new(),
                new(),
                new()
            }
        };
        public EmptyClass DepthDependentConnectionStrengths { get; private set; }
        public EmptyClass DpolyInterpolationTables { get; private set; }
        public EmptyClass PressureProfile { get; private set; }
        public TensionCrackProperties TensionCrackProperties { get; private set; } = new();
        public GenAnisoAdvancedOptions GenAnisoAdvancedOptions { get; private set; } = new();
        public List<Vertice> Vertices { get; private set; } = new();
        public List<Vertice> ProfileVertices { get; private set; } = new();
        public List<Cell> Cells { get; private set; } = new();
        public List<Cell> ProfileCells { get; private set; } = new();
        public EmptyClass Anchors { get; private set; }
        public BackAnalysisOfSupport BackAnalysisOfSupport { get; private set; } = new();
        public WaterTable WaterTable { get; private set; } = new();
        public EmptyClass DrawDownLine { get; private set; }
        public List<Piezo> Piezos { get; private set; } = new();
        public EmptyClass TensionCrack { get; private set; }
        public EmptyClass AnisoTropicSurfaces { get; private set; }
        public EmptyClass ThreeDAnisotropicSurfaces { get; private set; }
        public EmptyClass WeakLayers { get; private set; }
        public EmptyClass D0Polylines { get; private set; }
        public EmptyClass PorePressures { get; private set; }
        public Slope Slope { get; private set; } = new();
        public Exterior Exterior { get; private set; } = new();
        public EmptyClass Forces { get; private set; }
        public Analysis NonCircularAnalysis { get; private set; } = new();
        public Analysis CircularAnalysis { get; private set; } = new();
        public EmptyClass SearchFocus { get; private set; }
        public SlopeLimits SlopeLimits { get; private set; } = new();
        public EmptyClass Grids { get; private set; }
        public EmptyClass Centers { get; private set; }
        public EmptyClass Surfaces { get; private set; }
        public OptimizeSurfaces OptimizeSurfaces { get; private set; } = new();
        public GwmeshSetup GwmeshSetup { get; private set; } = new();
        public bool GwmeshAutoRefineMesh { get; private set; } = true;
        public int DiscRegions { get; private set; }
        public int NewDensityRegions { get; private set; }
        public FeaAnalysis FeaAnalysis { get; private set; } = new();
        public DischargeSectionsStart DischargeSectionsStart { get; private set; } = new();
        public EmptyClass DischargeSectionsEnd { get; private set; }
        public MaterialCount MaterialRandomVars { get; private set; } = new();
        public MaterialCount MaterialCorrelationMatrix { get; private set; } = new();
        public MaterialCount MaterialCorrelationEquated { get; private set; } = new();
        public MaterialCount MaterialCorrelationEntries { get; private set; } = new();
        public MaterialCount SupportRandomVars { get; private set; } = new();
        public SeismicRandomVar SeismicRandomVar { get; private set; } = new();
        public WaterTableStats WaterTableStats { get; private set; } = new();
        public DrawdownLineStats DrawdownLineStats { get; private set; } = new();
        public StatsParamsTensionCrack StatsParamsTensionCrack { get; private set; } = new();
        public List<StatsParamPiezo> StatsParamsPiezos { get; private set; } = new();
        public EmptyClass Interface { get; private set; }
        public List<MaterialGenHoekBrown> MaterialGenHoekBrown { get; private set; } = new();
        public List<MaterialGhbGsiDepth> MaterialGhbGsiDepth { get; private set; } = new();
        public List<MaterialGhbMiDepth> MaterialGhbMiDepth { get; private set; } = new();
        public List<MaterialGhbDDepth> MaterialGhbDDepth { get; private set; } = new();
        public List<MaterialProperty> MaterialProperties { get; private set; } = new();
        public List<Guid> GeometryGuiIds { get; private set; } = Enumerable.Range(0, 15).Select(i => Guid.NewGuid()).ToList();
        public List<GeometryInfo> GeometryInfo { get; private set; } = new();
        public SoilProfileStart SoilProfileStart { get; private set; } = new();
        public SoilBoreholesStart SoilBoreholesStart { get; private set; } = new();
        public SoilProfileFlagsStart SoilProfileFlagsStart { get; private set; } = new();
        public SlammerOptionsStart SlammerOptionsStart { get; private set; } = new();

        public SliFile() { }

        public SliFile(SliFile file)
        {
            ModelDescription = new(file.ModelDescription);
            SideForceFunction = new(file.SideForceFunction);
            MaterialTypes = file.MaterialTypes.Select(x => new MaterialType(x)).ToList();
            ShansepVariableFunctions = new(file.ShansepVariableFunctions);
            ModifiedSnowdenStrengthFunctions = new(file.ModifiedSnowdenStrengthFunctions);
            DesignStandardArrayDefinition = new(file.DesignStandardArrayDefinition);
            MaxCoverageSearch = new(file.MaxCoverageSearch);
            GridSearch = new(file.GridSearch);
            ThreepointSearch = new(file.ThreepointSearch);
            AutoslopeSearch = new(file.AutoslopeSearch);
            CuckooSearch = new(file.CuckooSearch);
            SimulatedAnnealingSearch = new(file.SimulatedAnnealingSearch);
            PsoSearch = new(file.PsoSearch);
            BlockSearch = new(file.BlockSearch);
            PathSearch = new(file.PathSearch);
            ThreeDSectionalInfo = new(file.ThreeDSectionalInfo);
            UserDefGeoManufacturerFileArray = file.UserDefGeoManufacturerFileArray;
            ProbabilitySettings = new(file.ProbabilitySettings);
            StrengthFunctions = file.StrengthFunctions.Select(x => new StrengthFunctionDefinition(x)).ToList();
            GeneralizedAnisotropicStrengthFunctions = new(file.GeneralizedAnisotropicStrengthFunctions);
            StartDiscreteStrengthFns = new(file.StartDiscreteStrengthFns);
            ShansepMaterialDependentFunctions = new(file.ShansepMaterialDependentFunctions);
            AnchorTypes = file.AnchorTypes.Select(x => new AnchorType(x)).ToList();
            AnchorRspileAssignments = new(file.AnchorRspileAssignments);
            AnchorRspilePileData = new(file.AnchorRspilePileData);
            TensionCrackProperties = new(file.TensionCrackProperties);
            GenAnisoAdvancedOptions = new(file.GenAnisoAdvancedOptions);
            Vertices = file.Vertices.Select(x => new Vertice(x)).ToList();
            ProfileVertices = file.ProfileVertices.Select(x => new Vertice(x)).ToList();
            Cells = file.Cells.Select(x => new Cell(x)).ToList();
            ProfileCells = file.ProfileCells.Select(x => new Cell(x)).ToList();
            BackAnalysisOfSupport = new(file.BackAnalysisOfSupport);
            WaterTable = new(file.WaterTable);
            Piezos = file.Piezos.Select(x => new Piezo(x)).ToList();
            Slope = new(file.Slope);
            Exterior = new(file.Exterior);
            NonCircularAnalysis = new(file.NonCircularAnalysis);
            CircularAnalysis = new(file.CircularAnalysis);
            SlopeLimits = new(file.SlopeLimits);
            OptimizeSurfaces = new(file.OptimizeSurfaces);
            GwmeshSetup = new(file.GwmeshSetup);
            GwmeshAutoRefineMesh = file.GwmeshAutoRefineMesh;
            FeaAnalysis = new(file.FeaAnalysis);
            DischargeSectionsStart = new(file.DischargeSectionsStart);
            MaterialRandomVars = new(file.MaterialRandomVars);
            MaterialCorrelationMatrix = new(file.MaterialCorrelationMatrix);
            MaterialCorrelationEquated = new(file.MaterialCorrelationEquated);
            MaterialCorrelationEntries = new(file.MaterialCorrelationEntries);
            SupportRandomVars = new(file.SupportRandomVars);
            SeismicRandomVar = new(file.SeismicRandomVar);
            WaterTableStats = new(file.WaterTableStats);
            DrawdownLineStats = new(file.DrawdownLineStats);
            StatsParamsTensionCrack = new(file.StatsParamsTensionCrack);
            StatsParamsPiezos = file.StatsParamsPiezos.Select(x => new StatsParamPiezo(x)).ToList();
            MaterialGenHoekBrown = file.MaterialGenHoekBrown.Select(x => new MaterialGenHoekBrown(x)).ToList();
            MaterialGhbGsiDepth = file.MaterialGhbGsiDepth.Select(x => new MaterialGhbGsiDepth(x)).ToList();
            MaterialGhbMiDepth = file.MaterialGhbMiDepth.Select(x => new MaterialGhbMiDepth(x)).ToList();
            MaterialGhbDDepth = file.MaterialGhbDDepth.Select(x => new MaterialGhbDDepth(x)).ToList();
            MaterialProperties = file.MaterialProperties.Select(x => new MaterialProperty(x)).ToList();
            GeometryGuiIds = file.GeometryGuiIds.Select(x => new Guid(x.ToByteArray())).ToList();
            GeometryInfo = file.GeometryInfo.Select(x => new GeometryInfo(x)).ToList();
            SoilProfileStart = new(file.SoilProfileStart);
            SoilBoreholesStart = new(file.SoilBoreholesStart);
            SoilProfileFlagsStart = new(file.SoilProfileFlagsStart);
            SlammerOptionsStart = new(file.SlammerOptionsStart);
        }

        public List<Vertice> AddVertices(List<PointD> points)
        {
            var vertices = new List<Vertice>();

            foreach (var point in points)
            {
                var existingVertice = Vertices
                    .FirstOrDefault(x => x.X == point.X && x.Y == point.Y);

                if (existingVertice != null)
                {
                    vertices.Add(existingVertice);
                    continue;
                }

                var vertice = new Vertice
                {
                    X = point.X,
                    Y = point.Y,
                    Index = Vertices.Count + 1
                };

                Vertices.Add(vertice);
                vertices.Add(vertice);
            }

            return vertices;
        }

        public Vertice AddVertice(PointD point)
        {
            var existingVertice = Vertices
                .FirstOrDefault(x => x.X == point.X && x.Y == point.Y);

            if (existingVertice != null)
            {
                return existingVertice;
            }

            var vertice = new Vertice
            {
                X = point.X,
                Y = point.Y,
                Index = Vertices.Count + 1
            };

            Vertices.Add(vertice);

            return vertice;
        }

        public Cell AddCell(Cell cell)
        {
            var existingCell = Cells
                .FirstOrDefault(x => x.Vertice1 == cell.Vertice1
                                   && x.Vertice2 == cell.Vertice2
                                   && x.Vertice3 == cell.Vertice3);

            if (existingCell != null)
            {
                return existingCell;
            }

            var newCell = new Cell
            {
                Index = Cells.Count + 1,
                Vertice1 = cell.Vertice1,
                Vertice2 = cell.Vertice2,
                Vertice3 = cell.Vertice3,
                Material = cell.Material
            };

            Cells.Add(newCell);

            return newCell;
        }

        public Slope AddSlope(List<int> vertices)
        {
            Slope = new Slope
            {
                Index = 1,
                Vertices = vertices
            };

            return Slope;
        }

        public Exterior AddExterior(List<int> vertices)
        {
            Exterior = new Exterior
            {
                Index = 1,
                Vertices = vertices
            };

            return Exterior;
        }

        public SlopeLimits AddSlopeLimits(Vertice point1, Vertice point2)
        {
            var slopeLimits = new SlopeLimits
            {
                X1 = Math.Round(point1.X, 11),
                Y1 = Math.Round(point1.Y, 11),
                X2 = Math.Round(point2.X, 11),
                Y2 = Math.Round(point2.Y, 11)
            };

            SlopeLimits = slopeLimits;

            return slopeLimits;
        }

        public GeometryInfo AddGeometryInfo(GeometryInfo geometryInfo)
        {
            var newGeometryInfo = new GeometryInfo
            {
                MaterialType = geometryInfo.MaterialType,
                EndFlag = geometryInfo.EndFlag,
                X = geometryInfo.X,
                Y = geometryInfo.Y,
                Disc = geometryInfo.Disc,
                ABI = geometryInfo.ABI,
                Visible = geometryInfo.Visible,
                Temporary = geometryInfo.Temporary
            };

            GeometryInfo.Add(newGeometryInfo);

            return newGeometryInfo;
        }

        public List<GeometryInfo> AddGeometryInfos(List<GeometryInfo> geometryInfos)
        {
            foreach (var geometryInfo in geometryInfos)
            {
                AddGeometryInfo(geometryInfo);
            }

            return GeometryInfo;
        }

        public void AddWaterTable(List<Vertice> vertices)
        {
            WaterTable.Vertices
                .AddRange(vertices.Select(x => (int)x.Index).ToList());
        }

        public void AddPiezo(List<Vertice> vertices)
        {
            var piezo = new Piezo
            {
                Vertices = vertices.Select(x => (int)x.Index).ToList(),
                Id = Piezos.Count + 1,
                UniqueId = Guid.NewGuid()
            };

            Piezos.Add(piezo);
        }

        public void AddMaterialProperties(MaterialProperty materialProperty)
        {
            MaterialProperties.Add(materialProperty);
        }

        public void AddStrengthFunction(StrengthFunctionDefinition strengthFunction)
        {
            StrengthFunctions.Add(strengthFunction);
        }

        public void AddShansepVariableFunctions(ShansepFunctionDefinition shansep)
        {
            ShansepVariableFunctions.Definitions.Add(shansep);
        }

        public void AddMaterial(MaterialType materialType, MaterialProperty materialProperty)
        {
            MaterialTypes.Add(materialType);
            MaterialProperties.Add(materialProperty);
            ModelDescription.NumMaterials++;
        }

        public static SliFile Load(string sliBase64)
        {
            var sliString = Encoding.UTF8.GetString(Convert.FromBase64String(sliBase64));

            return new()
            {
                Vertices = Parser.ParseVertices(sliString),
                ProfileVertices = Parser.ParseProfileVertices(sliString),
                Cells = Parser.ParseCells(sliString),
                ProfileCells = Parser.ParseProfileCells(sliString),
                Slope = Parser.ParseSlope(sliString),
                Exterior = Parser.ParseExterior(sliString),
                SlopeLimits = Parser.ParseSlopeLimits(sliString),
                GeometryInfo = Parser.ParseGeometryInfo(sliString),
                SoilProfileStart = Parser.ParseSoilProfileStart(sliString)
            };
        }

        public string Save()
        {
            var sb = new StringBuilder();

            AppendModelDescription(sb);
            AppendSideForceFunction(sb);
            AppendMaterialTypes(sb);
            AppendThreeDSectionalInfo(sb);
            AppendDesignStandardArrayDefinition(sb);
            AppendUserDefGeoManufacturerFileArray(sb);
            AppendProbabilitySettings(sb);
            AppendStrengthFunctions(sb);
            AppendGeneralizedAnisotropicStrengthFunctions(sb);
            AppendStartDiscreteStrengthFns(sb);
            AppendModifiedSnowdenStrengthFunctions(sb);
            AppendShansepVariableFunctions(sb);
            AppendShansepMaterialDependentFunctions(sb);
            AppendUserDefinedSupport(sb);
            AppendUserDefinedWaterMethod(sb);
            AppendUserDefinedBc(sb);
            AppendAnchorTypes(sb);
            AppendFrictionFactor(sb);
            AppendAnchorRspileAssignments(sb);
            AppendAnchorRspilePileData(sb);
            AppendDepthDependentConnectionStrengths(sb);
            AppendDpolyInterpolationTables(sb);
            AppendPressureProfile(sb);
            AppendTensionCrackProperties(sb);
            AppendGenAnisoAdvancedOptions(sb);
            AppendVertices(sb);
            AppendProfileVertices(sb);
            AppendCells(sb);
            AppendProfileCells(sb);
            AppendAnchors(sb);
            AppendBackAnalysisOfSupport(sb);
            AppendWaterTable(sb);
            AppendDrawdownLine(sb);
            AppendPiezos(sb);
            AppendTensionCrack(sb);
            AppendAnisotropicSurfaces(sb);
            AppendThreeDAnisotropicSurfaces(sb);
            AppendWeakLayers(sb);
            AppendD0Polylines(sb);
            AppendPorePressures(sb);
            AppendSlope(sb);
            AppendExterior(sb);
            AppendForces(sb);
            AppendNoncircularAnalysis(sb);
            AppendCircularAnalysis(sb);
            AppendSearchFocus(sb);
            AppendSlopeLimits(sb);
            AppendGrids(sb);
            AppendCenters(sb);
            AppendSurfaces(sb);
            AppendSearch(sb);
            AppendGwmeshSetup(sb);
            AppendGwmeshAutoRefineMesh(sb);
            AppendDiscRegions(sb);
            AppendNewDensityRegions(sb);
            AppendFeaAnalysis(sb);
            AppendDischargeSectionsStart(sb);
            AppendMaterialRandomVars(sb);
            AppendMaterialCorrelationMatrix(sb);
            AppendMaterialCorrelationEquated(sb);
            AppendMaterialCorrelationEntries(sb);
            AppendSupportRandomVars(sb);
            AppendSeismicRandomVar(sb);
            AppendWaterTableStats(sb);
            AppendDrawdownLineStats(sb);
            AppendStatsParamsTensionCrack(sb);
            AppendStatsParamsPiezos(sb);
            AppendInterface(sb);
            AppendMaterialGenHoekBrown(sb);
            AppendMaterialGhbGsiDepth(sb);
            AppendMaterialGhbMiDepth(sb);
            AppendDpolyInterpolationTables2(sb);
            AppendMaterialGhbDDepth(sb);
            AppendMaterialProperties(sb);
            AppendGeometryGuiIds(sb);
            AppendGeometryInfo(sb);
            AppendSoilProfileStart(sb);
            AppendSoilBoreholesStart(sb);
            AppendSoilProfileFlagsStart(sb);
            AppendSlammerOptionsStart(sb);

            return sb.ToString();
        }

        private void AppendModelDescription(StringBuilder sb)
        {
            sb.AppendLine(ModelDescription.ToString());
        }

        private void AppendSideForceFunction(StringBuilder sb)
        {
            sb.AppendLine(SideForceFunction.ToString());
        }

        private void AppendMaterialTypes(StringBuilder sb)
        {
            sb.AppendLine("material types:");
            MaterialTypes.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendThreeDSectionalInfo(StringBuilder sb)
        {
            sb.AppendLine(ThreeDSectionalInfo.ToString());
        }

        private void AppendDesignStandardArrayDefinition(StringBuilder sb)
        {
            sb.AppendLine(DesignStandardArrayDefinition.ToString());
        }

        private void AppendUserDefGeoManufacturerFileArray(StringBuilder sb)
        {
            sb.AppendLine($"user def geo manufacturer file array:\r\n{UserDefGeoManufacturerFileArray}");
        }

        private void AppendProbabilitySettings(StringBuilder sb)
        {
            sb.AppendLine(ProbabilitySettings.ToString());
        }

        private void AppendStrengthFunctions(StringBuilder sb)
        {
            sb.AppendLine("\r\nstrength functions:");
            StrengthFunctions.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendGeneralizedAnisotropicStrengthFunctions(StringBuilder sb)
        {
            sb.AppendLine(GeneralizedAnisotropicStrengthFunctions.ToString());
        }

        private void AppendStartDiscreteStrengthFns(StringBuilder sb)
        {
            sb.AppendLine(StartDiscreteStrengthFns.ToString());
            sb.AppendLine("\r\nend discrete strength fns:");
        }

        private void AppendModifiedSnowdenStrengthFunctions(StringBuilder sb)
        {
            sb.AppendLine(ModifiedSnowdenStrengthFunctions.ToString());
        }

        private void AppendShansepVariableFunctions(StringBuilder sb)
        {
            sb.AppendLine(ShansepVariableFunctions.ToString());
        }

        private void AppendShansepMaterialDependentFunctions(StringBuilder sb)
        {
            sb.AppendLine(ShansepMaterialDependentFunctions.ToString());
        }

        private void AppendUserDefinedSupport(StringBuilder sb)
        {
            sb.AppendLine("\r\nuser defined support:");
        }

        private void AppendUserDefinedWaterMethod(StringBuilder sb)
        {
            sb.AppendLine("\r\nuser defined water method:");
        }

        private void AppendUserDefinedBc(StringBuilder sb)
        {
            sb.AppendLine("\r\nuser defined bc:");
        }

        private void AppendAnchorTypes(StringBuilder sb)
        {
            sb.AppendLine("\r\nanchor types:");
            AnchorTypes.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendFrictionFactor(StringBuilder sb)
        {
            sb.AppendLine("\r\nfriction factor:");
        }

        private void AppendAnchorRspileAssignments(StringBuilder sb)
        {
            sb.AppendLine(AnchorRspileAssignments.ToString());
        }

        private void AppendAnchorRspilePileData(StringBuilder sb)
        {
            sb.AppendLine(AnchorRspilePileData.ToString());
        }

        private void AppendDepthDependentConnectionStrengths(StringBuilder sb)
        {
            sb.AppendLine("\r\ndepth dependent connection strengths:");
        }

        private void AppendDpolyInterpolationTables(StringBuilder sb)
        {
            sb.AppendLine("\r\ndpoly interpolation tables:");
        }

        private void AppendPressureProfile(StringBuilder sb)
        {
            sb.AppendLine("\r\npressure profile:");
        }

        private void AppendTensionCrackProperties(StringBuilder sb)
        {
            sb.AppendLine(TensionCrackProperties.ToString());
        }

        private void AppendGenAnisoAdvancedOptions(StringBuilder sb)
        {
            sb.AppendLine(GenAnisoAdvancedOptions.ToString());
        }

        private void AppendVertices(StringBuilder sb)
        {
            sb.AppendLine("vertices:");
            Vertices.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendProfileVertices(StringBuilder sb)
        {
            sb.AppendLine("\r\nprofile_vertices:");
            ProfileVertices.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendCells(StringBuilder sb)
        {
            sb.AppendLine("\r\ncells:");
            Cells.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendProfileCells(StringBuilder sb)
        {
            sb.AppendLine("\r\nprofile_cells:");
            ProfileCells.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendAnchors(StringBuilder sb)
        {
            sb.AppendLine("\r\nanchors:");
        }

        private void AppendBackAnalysisOfSupport(StringBuilder sb)
        {
            sb.AppendLine(BackAnalysisOfSupport.ToString());
        }

        private void AppendWaterTable(StringBuilder sb)
        {
            sb.AppendLine(WaterTable.ToString());
        }

        private void AppendDrawdownLine(StringBuilder sb)
        {
            sb.AppendLine("\r\ndrawdown line:");
        }

        private void AppendPiezos(StringBuilder sb)
        {
            sb.AppendLine("\r\npiezos:");
            Piezos.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendTensionCrack(StringBuilder sb)
        {
            sb.AppendLine("\r\ntension crack:");
        }

        private void AppendAnisotropicSurfaces(StringBuilder sb)
        {
            sb.AppendLine("\r\nanisotropic surfaces:");
        }

        private void AppendThreeDAnisotropicSurfaces(StringBuilder sb)
        {
            sb.AppendLine("\r\n3d anisotropic surfaces:");
        }

        private void AppendWeakLayers(StringBuilder sb)
        {
            sb.AppendLine("\r\nweak layers:");
        }

        private void AppendD0Polylines(StringBuilder sb)
        {
            sb.AppendLine("\r\nd0 polylines:");
        }

        private void AppendPorePressures(StringBuilder sb)
        {
            sb.AppendLine("\r\npore pressures:");
        }

        private void AppendSlope(StringBuilder sb)
        {
            sb.AppendLine(Slope.ToString());
        }

        private void AppendExterior(StringBuilder sb)
        {
            sb.AppendLine(Exterior.ToString());
        }

        private void AppendForces(StringBuilder sb)
        {
            sb.AppendLine("\r\nforces:");
        }

        private void AppendNoncircularAnalysis(StringBuilder sb)
        {
            sb.AppendLine("\r\nnoncircular analysis:");
            sb.AppendLine(NonCircularAnalysis.ToString());
        }

        private void AppendCircularAnalysis(StringBuilder sb)
        {
            sb.AppendLine("\r\ncircular analysis:");
            sb.AppendLine(CircularAnalysis.ToString());
        }

        private void AppendSearchFocus(StringBuilder sb)
        {
            sb.AppendLine("\r\nsearch focus:");
        }

        private void AppendSlopeLimits(StringBuilder sb)
        {
            sb.AppendLine(SlopeLimits.ToString());
        }

        private void AppendGrids(StringBuilder sb)
        {
            sb.AppendLine("\r\ngrids:");
        }

        private void AppendCenters(StringBuilder sb)
        {
            sb.AppendLine("\r\ncenters:");
        }

        private void AppendSurfaces(StringBuilder sb)
        {
            sb.AppendLine("\r\nsurfaces:");
        }

        private void AppendSearch(StringBuilder sb)
        {
            sb.AppendLine(GridSearch.ToString());
            sb.AppendLine(BlockSearch.ToString());
            sb.AppendLine(PathSearch.ToString());
            sb.AppendLine(ThreepointSearch.ToString());
            sb.AppendLine(MaxCoverageSearch.ToString());
            sb.AppendLine(SimulatedAnnealingSearch.ToString());
            sb.AppendLine(AutoslopeSearch.ToString());
            sb.AppendLine(CuckooSearch.ToString());
            sb.AppendLine(PsoSearch.ToString());
            sb.AppendLine(OptimizeSurfaces.ToString());
        }

        private void AppendGwmeshSetup(StringBuilder sb)
        {
            sb.AppendLine(GwmeshSetup.ToString());
        }

        private void AppendGwmeshAutoRefineMesh(StringBuilder sb)
        {
            sb.AppendLine($"\r\ngwmesh auto refine mesh:\r\n{Convert.ToInt32(GwmeshAutoRefineMesh)}");
        }

        private void AppendDiscRegions(StringBuilder sb)
        {
            sb.AppendLine($"\r\ndisc regions:\r\n  {DiscRegions}");
        }

        private void AppendNewDensityRegions(StringBuilder sb)
        {
            sb.AppendLine($"\r\nnew density regions:\r\n  {NewDensityRegions}");
        }

        private void AppendFeaAnalysis(StringBuilder sb)
        {
            sb.AppendLine(FeaAnalysis.ToString());
        }

        private void AppendDischargeSectionsStart(StringBuilder sb)
        {
            sb.AppendLine(DischargeSectionsStart.ToString());
        }

        private void AppendMaterialRandomVars(StringBuilder sb)
        {
            sb.AppendLine("\r\nmaterial random vars:");
            sb.AppendLine(MaterialRandomVars.ToString());
        }

        private void AppendMaterialCorrelationMatrix(StringBuilder sb)
        {
            sb.AppendLine("\r\nmaterial correlation matrix:");
            sb.AppendLine(MaterialCorrelationMatrix.ToString());
        }

        private void AppendMaterialCorrelationEquated(StringBuilder sb)
        {
            sb.AppendLine("\r\nmaterial correlation equated:");
            sb.AppendLine(MaterialCorrelationEquated.ToString());
        }

        private void AppendMaterialCorrelationEntries(StringBuilder sb)
        {
            sb.AppendLine("\r\nmaterial correlation entries:");
            sb.AppendLine(MaterialCorrelationEntries.ToString());
        }

        private void AppendSupportRandomVars(StringBuilder sb)
        {
            sb.AppendLine("\r\nsupport random vars:");
            sb.AppendLine(SupportRandomVars.ToString());
        }

        private void AppendSeismicRandomVar(StringBuilder sb)
        {
            sb.AppendLine(SeismicRandomVar.ToString());
        }

        private void AppendWaterTableStats(StringBuilder sb)
        {
            sb.AppendLine(WaterTableStats.ToString());
        }

        private void AppendDrawdownLineStats(StringBuilder sb)
        {
            sb.AppendLine(DrawdownLineStats.ToString());
        }

        private void AppendStatsParamsTensionCrack(StringBuilder sb)
        {
            sb.AppendLine(StatsParamsTensionCrack.ToString());
        }

        private void AppendStatsParamsPiezos(StringBuilder sb)
        {
            sb.AppendLine("\r\nstats params piezos:");
            StatsParamsPiezos.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendInterface(StringBuilder sb)
        {
            sb.AppendLine("\r\ninterface:");
        }

        private void AppendMaterialGenHoekBrown(StringBuilder sb)
        {
            sb.AppendLine("\r\nmaterial gen. hoek-brown:");
            MaterialGenHoekBrown.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendMaterialGhbGsiDepth(StringBuilder sb)
        {
            sb.AppendLine("\r\nmaterial ghb gsi depth:");
            MaterialGhbGsiDepth.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendMaterialGhbMiDepth(StringBuilder sb)
        {
            sb.AppendLine("\r\nmaterial ghb mi depth:");
            MaterialGhbMiDepth.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendDpolyInterpolationTables2(StringBuilder sb)
        {
            sb.AppendLine("\r\ndpoly interpolation tables:");
        }

        private void AppendMaterialGhbDDepth(StringBuilder sb)
        {
            sb.AppendLine("\r\nmaterial ghb D depth:");
            MaterialGhbDDepth.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendMaterialProperties(StringBuilder sb)
        {
            sb.AppendLine("\r\nmaterial properties:");
            MaterialProperties.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendGeometryGuiIds(StringBuilder sb)
        {
            sb.AppendLine("\r\ngeometry gui_ids:");
            GeometryGuiIds.ForEach(x => sb.AppendLine($"guid: {{{x.ToString()}}}"));
        }

        private void AppendGeometryInfo(StringBuilder sb)
        {
            sb.AppendLine("\r\ngeometry info:");
            GeometryInfo.ForEach(x => sb.AppendLine(x.ToString()));
        }

        private void AppendSoilProfileStart(StringBuilder sb)
        {
            sb.AppendLine(SoilProfileStart.ToString());
        }

        private void AppendSoilBoreholesStart(StringBuilder sb)
        {
            sb.AppendLine(SoilBoreholesStart.ToString());
        }

        private void AppendSoilProfileFlagsStart(StringBuilder sb)
        {
            sb.AppendLine(SoilProfileFlagsStart.ToString());
        }

        private void AppendSlammerOptionsStart(StringBuilder sb)
        {
            sb.AppendLine(SlammerOptionsStart.ToString());
        }
    }
}
using System.Text;
using Application.Apis.Clients;
using Dxf.Core.Extensions;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Geometry.Core;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using Serilog;
using Slide.Core;
using Slide.Core.Objects.Sli;
using TriangleNet.Geometry;
using TriangleNet.Meshing;
using static Workflow.Constants.SectionWorkFlow;
using Activity = Elsa.Services.Activity;
using File = Application.Apis._Shared.File;

namespace Workflow.Section.Activities
{
    public class CreateInitialSliFileActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public CreateInitialSliFileActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var sectionId = (context.GetTransientVariable<Domain.Messages.Commands.Section.CreateSliFile>(Variables.Command)).SectionId;

                var section = await _clientsApiService
                    .GetSectionById(sectionId);

                if (section == null)
                {
                    return Done();
                }

                foreach (var sectionReview in section.Reviews)
                {
                    if (sectionReview.ConstructionStages.Any())
                    {
                        foreach (var constructionStage in sectionReview.ConstructionStages)
                        {
                            var constructionStageSli = MountSliFile(constructionStage.Drawing);

                            if (constructionStageSli == null)
                            {
                                continue;
                            }

                            var result = await _clientsApiService.AddSliToSectionReview(new()
                            {
                                SectionId = sectionId,
                                ReviewId = sectionReview.Id,
                                ConstructionStageId = constructionStage.Id,
                                Sli = constructionStageSli
                            });

                            if (!result.IsSuccessStatusCode)
                            {
                                Log.Error("Error adding SLI to construction stage.");
                                return Fault("Error adding SLI to construction stage.");
                            }
                        }

                        continue;
                    }

                    var reviewSli = MountSliFile(sectionReview.Drawing);

                    if (reviewSli == null)
                    {
                        continue;
                    }

                    var resultSectionReview = await _clientsApiService.AddSliToSectionReview(new()
                    {
                        SectionId = sectionId,
                        ReviewId = sectionReview.Id,
                        Sli = reviewSli
                    });

                    if (!resultSectionReview.IsSuccessStatusCode)
                    {
                        Log.Error("Error adding SLI to section review.");
                        return Fault("Error adding SLI to section review.");
                    }
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in CreateInitialSliFileActivity");
                return Fault(e);
            }
        }

        private File MountSliFile(File dxfFile)
        {
            if (dxfFile == null || string.IsNullOrEmpty(dxfFile.Base64))
            {
                return null;
            }

            var dxf = new DxfFile();
            dxf = dxf.LoadWithBase64(dxfFile.Base64);

            var externalEntities = dxf.Entities.Where(e => e.Layer.ToLower() == "external").ToList();
            var materialEntities = dxf.Entities.Where(e => e.Layer.ToLower() == "material").ToList();

            var externalVertices = externalEntities
                .Where(x => x.EntityType == DxfEntityType.LwPolyline)
                .Select(x => (DxfLwPolyline)x)
                .SelectMany(x => x.Vertices)
                .Select(x => new Vertice()
                {
                    X = x.X,
                    Y = x.Y
                })
                .ToList();

            externalVertices.AddRange(externalEntities
                .Where(x => x.EntityType == DxfEntityType.Polyline)
                .Select(x => (DxfPolyline)x)
                .SelectMany(x => x.Vertices)
                .Select(x => new Vertice()
                {
                    X = x.Location.X,
                    Y = x.Location.Y
                })
                .ToList());

            externalVertices.AddRange(externalEntities
                .Where(x => x.EntityType == DxfEntityType.Line)
                .SelectMany(x => new[]
                {
                            new Vertice()
                            {
                                X = ((DxfLine)x).P1.X,
                                Y = ((DxfLine)x).P1.Y
                            },
                            new Vertice()
                            {
                                X = ((DxfLine)x).P2.X,
                                Y = ((DxfLine)x).P2.Y
                            },
                })
                .ToList());

            if (externalVertices.Count == 0)
            {
                Log.Information("No external vertices found in the DXF file.");
                return null;
            }

            // Send the first vertice to the end of the list
            externalVertices = externalVertices.Skip(1).Concat(externalVertices.Take(1)).ToList();

            var materials = new List<(Vertice, int)>();
            var groupIndex = 1;

            foreach (var entity in materialEntities)
            {
                if (entity.EntityType == DxfEntityType.LwPolyline)
                {
                    var polyline = (DxfLwPolyline)entity;

                    foreach (var vertex in polyline.Vertices)
                    {
                        materials.Add((new Vertice()
                        {
                            X = vertex.X,
                            Y = vertex.Y
                        }, groupIndex));
                    }

                    groupIndex++;
                }

                if (entity.EntityType == DxfEntityType.Polyline)
                {
                    var polyline = (DxfPolyline)entity;

                    foreach (var vertex in polyline.Vertices)
                    {
                        materials.Add((new Vertice()
                        {
                            X = vertex.Location.X,
                            Y = vertex.Location.Y
                        }, groupIndex));
                    }

                    groupIndex++;
                }

                if (entity.EntityType == DxfEntityType.Line)
                {
                    var line = (DxfLine)entity;

                    materials.Add((new Vertice()
                    {
                        X = line.P1.X,
                        Y = line.P1.Y
                    }, groupIndex));

                    materials.Add((new Vertice()
                    {
                        X = line.P2.X,
                        Y = line.P2.Y
                    }, groupIndex));

                    groupIndex++;
                }
            }

            if (materials.Count == 0)
            {
                Log.Information("No materials found in the DXF file.");
                return null;
            }

            var polygon = BuildRobustPolygon(externalVertices, materials);

            var mesh = PerformTriangulation(polygon);

            var sliFile = new SliFile();

            sliFile.AddVertices(externalVertices.Select(x => new PointD(x.X, x.Y)).ToList());
            sliFile.AddVertices(materials.Select(x => new PointD(x.Item1.X, x.Item1.Y)).ToList());

            foreach (var triangle in mesh.Triangles)
            {
                var vertice1 = triangle.GetVertex(0);
                var vertice2 = triangle.GetVertex(1);
                var vertice3 = triangle.GetVertex(2);

                var sliVertice1 = sliFile.Vertices.FirstOrDefault(x => x.X == vertice1.X && x.Y == vertice1.Y);

                if (sliVertice1 == null)
                {
                    var newVertice = new PointD(vertice1.X, vertice1.Y);
                    sliVertice1 = sliFile.AddVertice(newVertice);
                }

                var sliVertice2 = sliFile.Vertices.FirstOrDefault(x => x.X == vertice2.X && x.Y == vertice2.Y);

                if (sliVertice2 == null)
                {
                    var newVertice = new PointD(vertice2.X, vertice2.Y);
                    sliVertice2 = sliFile.AddVertice(newVertice);
                }

                var sliVertice3 = sliFile.Vertices.FirstOrDefault(x => x.X == vertice3.X && x.Y == vertice3.Y);

                if (sliVertice3 == null)
                {
                    var newVertice = new PointD(vertice3.X, vertice3.Y);
                    sliVertice3 = sliFile.AddVertice(newVertice);
                }

                sliFile.AddCell(new()
                {
                    Vertice1 = (int)sliVertice1.Index,
                    Vertice2 = (int)sliVertice2.Index,
                    Vertice3 = (int)sliVertice3.Index,
                    Material = "soil1"
                });
            }

            var externalVerticesIndex = externalVertices
                .Select(x => (int)sliFile.Vertices.First(v => v.X == x.X && v.Y == x.Y).Index)
                .Distinct()
                .ToList();

            sliFile.AddExterior(externalVerticesIndex);

            var minX = externalVertices.Min(x => x.X);
            var maxX = externalVertices.Max(x => x.X);

            var vertexWithSmalledXAndLargestY = externalVertices.Where(x => x.X == minX).OrderByDescending(x => x.Y).First();
            var vertexWithLargestXAndLargestY = externalVertices.Where(x => x.X == maxX).OrderByDescending(x => x.Y).First();

            var upperVertices = externalVertices
                .Where(x => x.X > vertexWithSmalledXAndLargestY.X && x.X < vertexWithLargestXAndLargestY.X && (x.Y >= vertexWithSmalledXAndLargestY.Y || x.Y >= vertexWithLargestXAndLargestY.Y)
                || (x.X == vertexWithSmalledXAndLargestY.X && x.Y == vertexWithSmalledXAndLargestY.Y || x.X == vertexWithLargestXAndLargestY.X && x.Y == vertexWithLargestXAndLargestY.Y))
                .OrderBy(x => x.X)
                .ToList();

            var upperVerticesIndex = upperVertices
                .Select(x => (int)sliFile.Vertices.First(v => v.X == x.X && v.Y == x.Y).Index)
                .Distinct()
                .ToList();

            sliFile.AddSlope(upperVerticesIndex);

            upperVertices = upperVertices.OrderBy(x => x.X).ToList();

            var upperVerticeWithMinX = upperVertices.First();
            var upperVerticeWithMaxX = upperVertices.Last();

            sliFile.AddSlopeLimits(upperVerticeWithMinX, upperVerticeWithMaxX);

            var lastExternalVertice = externalVertices.Last();

            sliFile.AddGeometryInfos(externalVertices.Select(x => new GeometryInfo()
            {
                X = x.X,
                Y = x.Y,
                MaterialType = 0,
                EndFlag = x.X == lastExternalVertice.X && x.Y == lastExternalVertice.Y && x.Index == lastExternalVertice.Index ? true : false,
            }).ToList());

            foreach (var group in materials.GroupBy(x => x.Item2))
            {
                for (int i = 0; i < group.Count(); i++)
                {
                    var element1 = group.ElementAtOrDefault(i).Item1;
                    var element2 = group.ElementAtOrDefault(i + 1).Item1;

                    if (element1 != null && element2 != null)
                    {
                        sliFile.AddGeometryInfo(new()
                        {
                            X = element1.X,
                            Y = element1.Y,
                            MaterialType = 3,
                            EndFlag = false
                        });
                    }
                    else
                    {
                        sliFile.AddGeometryInfo(new()
                        {
                            X = element1.X,
                            Y = element1.Y,
                            MaterialType = 3,
                            EndFlag = true
                        });
                    }
                }
            }

            sliFile.AddMaterialProperties(new()
            {
                Name = "Support 1",
                Red = 0,
                Green = 0,
                Blue = 255,
                Guid = Guid.NewGuid()
            });

            sliFile.AddMaterialProperties(new()
            {
                Name = "Support 2",
                Red = 0,
                Green = 255,
                Blue = 0,
                Guid = Guid.NewGuid()
            });

            var textBytes = Encoding.UTF8.GetBytes(sliFile.Save());

            return new()
            {
                Base64 = Convert.ToBase64String(textBytes),
                Name = $"{DateTime.Now.ToString("ddMMyyyy_HHmmss")}.sli"
            };
        }

        private static Polygon BuildRobustPolygon(List<Vertice> externalVertices, List<(Vertice, int)> materials)
        {
            const double tolerance = 1e-10;
            var polygon = new Polygon();

            // Add external contour with validated vertices
            var externalVerticesDeduped = DeduplicateVertices(externalVertices, tolerance);
            if (externalVerticesDeduped.Count >= 3)
            {
                polygon.Add(new Contour(externalVerticesDeduped.Select(x => new Vertex(x.X, x.Y, 1)), 1));
            }

            // Process material segments with validation
            var validSegments = new HashSet<(double x1, double y1, double x2, double y2)>();
            var polygonPoints = new HashSet<(double x, double y)>();

            // Add external vertices to polygon points
            foreach (var vertex in externalVerticesDeduped)
            {
                polygonPoints.Add((vertex.X, vertex.Y));
            }

            var materialSegments = materials
                .GroupBy(x => x.Item2)
                .SelectMany(group => group.Take(group.Count() - 1)
                .Zip(group.Skip(1), (element1, element2) => new
                {
                    Element1 = element1.Item1,
                    Element2 = element2.Item1
                })
                .Where(data => data.Element1 != null && data.Element2 != null)
                .Select(data => new
                {
                    Vertex1 = data.Element1,
                    Vertex2 = data.Element2
                }))
                .ToList();

            foreach (var segment in materialSegments)
            {
                var v1 = segment.Vertex1;
                var v2 = segment.Vertex2;

                // Skip degenerate segments (same start and end point)
                if (Math.Abs(v1.X - v2.X) < tolerance && Math.Abs(v1.Y - v2.Y) < tolerance)
                {
                    continue;
                }

                // Create normalized segment key for duplicate detection
                var segmentKey = v1.X < v2.X || (Math.Abs(v1.X - v2.X) < tolerance && v1.Y < v2.Y)
                    ? (v1.X, v1.Y, v2.X, v2.Y)
                    : (v2.X, v2.Y, v1.X, v1.Y);

                // Skip duplicate segments
                if (validSegments.Contains(segmentKey))
                {
                    continue;
                }

                validSegments.Add(segmentKey);

                // Add segment to polygon
                var triangleVertex1 = new Vertex(v1.X, v1.Y);
                var triangleVertex2 = new Vertex(v2.X, v2.Y);
                polygon.Add(new Segment(triangleVertex1, triangleVertex2, 1), 1);

                // Add unique vertices to polygon points
                polygonPoints.Add((v1.X, v1.Y));
                polygonPoints.Add((v2.X, v2.Y));
            }

            // Add all unique points to polygon
            foreach (var point in polygonPoints)
            {
                polygon.Points.Add(new Vertex(point.x, point.y));
            }

            return polygon;
        }

        private static List<Vertice> DeduplicateVertices(List<Vertice> vertices, double tolerance)
        {
            var result = new List<Vertice>();

            foreach (var vertex in vertices)
            {
                var isDuplicate = result.Any(existing =>
                    Math.Abs(existing.X - vertex.X) < tolerance &&
                    Math.Abs(existing.Y - vertex.Y) < tolerance);

                if (!isDuplicate)
                {
                    result.Add(vertex);
                }
            }

            return result;
        }

        private static IMesh PerformTriangulation(Polygon polygon)
        {
            // Validate polygon before triangulation
            if (!ValidatePolygon(polygon))
            {
                Log.Warning("Polygon validation failed. Attempting to clean polygon.");
                polygon = CleanPolygon(polygon);

                if (!ValidatePolygon(polygon))
                {
                    throw new InvalidOperationException("Unable to create valid polygon for triangulation.");
                }
            }

            var optionsCollection = new ConstraintOptions[]
            {
                new()
                {
                    ConformingDelaunay = true,
                    Convex = false,
                    SegmentSplitting = 2
                },
                new()
                {
                    ConformingDelaunay = true,
                    Convex = false,
                    SegmentSplitting = 0
                },
                new()
                {
                    ConformingDelaunay = true,
                    Convex = false,
                    SegmentSplitting = 1
                },
                // Additional fallback options
                new()
                {
                    ConformingDelaunay = false,
                    Convex = false,
                    SegmentSplitting = 0
                },
                new()
                {
                    ConformingDelaunay = false,
                    Convex = true,
                    SegmentSplitting = 0
                }
            };

            Exception lastException = null;

            foreach (var option in optionsCollection)
            {
                try
                {
                    Log.Information("Attempting triangulation with ConformingDelaunay: {ConformingDelaunay}, Convex: {Convex}, SegmentSplitting: {SegmentSplitting}",
                        option.ConformingDelaunay, option.Convex, option.SegmentSplitting);

                    var mesh = polygon.Triangulate(option);

                    if (mesh.Triangles.Count() > 0)
                    {
                        Log.Information("Triangulation successful with {TriangleCount} triangles", mesh.Triangles.Count());
                        return mesh;
                    }

                    Log.Warning("Triangulation produced no triangles");
                }
                catch (Exception e)
                {
                    lastException = e;
                    Log.Warning(e, "Triangulation failed with ConformingDelaunay: {ConformingDelaunay}, Convex: {Convex}, SegmentSplitting: {SegmentSplitting}",
                        option.ConformingDelaunay, option.Convex, option.SegmentSplitting);
                }
            }

            // Log detailed polygon information for debugging
            Log.Error("All triangulation attempts failed. Polygon details: Points={PointCount}, Segments={SegmentCount}, Holes={HoleCount}",
                polygon.Points.Count, polygon.Segments.Count, polygon.Holes.Count);

            throw new InvalidOperationException($"Unable to perform triangulation with any option. Last error: {lastException?.Message}", lastException);
        }

        private static bool ValidatePolygon(Polygon polygon)
        {
            try
            {
                // Basic validation checks
                if (polygon.Points.Count < 3)
                {
                    Log.Warning("Polygon has fewer than 3 points");
                    return false;
                }

                // Check for duplicate points
                const double tolerance = 1e-10;
                var uniquePoints = new HashSet<(double x, double y)>();

                foreach (var point in polygon.Points)
                {
                    var roundedPoint = (Math.Round(point.X / tolerance) * tolerance, Math.Round(point.Y / tolerance) * tolerance);
                    if (!uniquePoints.Add(roundedPoint))
                    {
                        Log.Warning("Polygon contains duplicate points at ({X}, {Y})", point.X, point.Y);
                        return false;
                    }
                }

                // Check for degenerate segments
                foreach (var segment in polygon.Segments)
                {
                    var p1 = segment.GetVertex(0);
                    var p2 = segment.GetVertex(1);

                    if (Math.Abs(p1.X - p2.X) < tolerance && Math.Abs(p1.Y - p2.Y) < tolerance)
                    {
                        Log.Warning("Polygon contains degenerate segment at ({X}, {Y})", p1.X, p1.Y);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception e)
            {
                Log.Warning(e, "Error during polygon validation");
                return false;
            }
        }

        private static Polygon CleanPolygon(Polygon originalPolygon)
        {
            const double tolerance = 1e-10;
            var cleanedPolygon = new Polygon();

            try
            {
                // Clean and deduplicate points
                var uniquePoints = new Dictionary<(double x, double y), Vertex>();

                foreach (var point in originalPolygon.Points)
                {
                    var key = (Math.Round(point.X / tolerance) * tolerance, Math.Round(point.Y / tolerance) * tolerance);
                    if (!uniquePoints.ContainsKey(key))
                    {
                        uniquePoints[key] = new Vertex(key.Item1, key.Item2);
                    }
                }

                // Add cleaned points
                foreach (var vertex in uniquePoints.Values)
                {
                    cleanedPolygon.Points.Add(vertex);
                }

                // Clean and validate segments
                var validSegments = new HashSet<(double x1, double y1, double x2, double y2)>();

                foreach (var segment in originalPolygon.Segments)
                {
                    var p1 = segment.GetVertex(0);
                    var p2 = segment.GetVertex(1);

                    // Skip degenerate segments
                    if (Math.Abs(p1.X - p2.X) < tolerance && Math.Abs(p1.Y - p2.Y) < tolerance)
                    {
                        continue;
                    }

                    // Create normalized segment key
                    var segmentKey = p1.X < p2.X || (Math.Abs(p1.X - p2.X) < tolerance && p1.Y < p2.Y)
                        ? (p1.X, p1.Y, p2.X, p2.Y)
                        : (p2.X, p2.Y, p1.X, p1.Y);

                    if (validSegments.Add(segmentKey))
                    {
                        var cleanVertex1 = new Vertex(segmentKey.Item1, segmentKey.Item2);
                        var cleanVertex2 = new Vertex(segmentKey.Item3, segmentKey.Item4);
                        cleanedPolygon.Add(new Segment(cleanVertex1, cleanVertex2, segment.Label), segment.Label);
                    }
                }

                // Note: TriangleNet.Geometry.Polygon doesn't have a Contours collection
                // Contours are added via the Add(Contour) method and managed internally

                Log.Information("Polygon cleaned: Original points={OriginalPoints}, segments={OriginalSegments}; Cleaned points={CleanedPoints}, segments={CleanedSegments}",
                    originalPolygon.Points.Count, originalPolygon.Segments.Count,
                    cleanedPolygon.Points.Count, cleanedPolygon.Segments.Count);

                return cleanedPolygon;
            }
            catch (Exception e)
            {
                Log.Error(e, "Error during polygon cleaning");
                return originalPolygon; // Return original if cleaning fails
            }
        }
    }
}
